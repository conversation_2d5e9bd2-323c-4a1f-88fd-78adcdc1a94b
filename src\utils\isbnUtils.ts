/**
 * ISBN Detection and Validation Utilities
 * Handles ISBN-10 and ISBN-13 detection, validation, and normalization
 */

export interface ISBNInfo {
  isbn10?: string;
  isbn13?: string;
  isValid: boolean;
  detectedFrom: 'name' | 'description' | 'field' | 'manual';
}

/**
 * Regex patterns for ISBN detection
 */
const ISBN_PATTERNS = {
  // ISBN-13: 978-0-123456-78-9 or 9780123456789
  isbn13: /(?:ISBN[-\s]?13[:\s]?)?(?:978|979)[-\s]?(?:\d[-\s]?){9}\d/gi,
  // ISBN-10: 0-123456-78-9 or 0123456789
  isbn10: /(?:ISBN[-\s]?10[:\s]?)?(?:\d[-\s]?){9}[\dXx]/gi,
  // General ISBN pattern
  general: /ISBN[-\s]?(?:13|10)?[:\s]?(?:978|979)?[-\s]?(?:\d[-\s]?){9,12}[\dXx]/gi
};

/**
 * Clean and normalize ISBN string
 */
export function cleanISBN(isbn: string): string {
  return isbn.replace(/[^\dXx]/gi, '').toUpperCase();
}

/**
 * Validate ISBN-10 checksum
 */
export function validateISBN10(isbn: string): boolean {
  const cleaned = cleanISBN(isbn);
  if (cleaned.length !== 10) return false;

  let sum = 0;
  for (let i = 0; i < 9; i++) {
    const digit = parseInt(cleaned[i]);
    if (isNaN(digit)) return false;
    sum += digit * (10 - i);
  }

  const checkDigit = cleaned[9];
  const calculatedCheck = (11 - (sum % 11)) % 11;
  
  if (calculatedCheck === 10) {
    return checkDigit === 'X';
  }
  return parseInt(checkDigit) === calculatedCheck;
}

/**
 * Validate ISBN-13 checksum
 */
export function validateISBN13(isbn: string): boolean {
  const cleaned = cleanISBN(isbn);
  if (cleaned.length !== 13) return false;
  if (!cleaned.startsWith('978') && !cleaned.startsWith('979')) return false;

  let sum = 0;
  for (let i = 0; i < 12; i++) {
    const digit = parseInt(cleaned[i]);
    if (isNaN(digit)) return false;
    sum += digit * (i % 2 === 0 ? 1 : 3);
  }

  const checkDigit = parseInt(cleaned[12]);
  const calculatedCheck = (10 - (sum % 10)) % 10;
  
  return checkDigit === calculatedCheck;
}

/**
 * Convert ISBN-10 to ISBN-13
 */
export function isbn10ToISBN13(isbn10: string): string | null {
  const cleaned = cleanISBN(isbn10);
  if (!validateISBN10(cleaned)) return null;

  const base = '978' + cleaned.substring(0, 9);
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    sum += parseInt(base[i]) * (i % 2 === 0 ? 1 : 3);
  }
  const checkDigit = (10 - (sum % 10)) % 10;
  
  return base + checkDigit;
}

/**
 * Convert ISBN-13 to ISBN-10
 */
export function isbn13ToISBN10(isbn13: string): string | null {
  const cleaned = cleanISBN(isbn13);
  if (!validateISBN13(cleaned) || !cleaned.startsWith('978')) return null;

  const base = cleaned.substring(3, 12);
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(base[i]) * (10 - i);
  }
  const checkDigit = (11 - (sum % 11)) % 11;
  
  if (checkDigit === 10) {
    return base + 'X';
  }
  return base + checkDigit;
}

/**
 * Extract ISBNs from text using regex patterns
 */
export function extractISBNsFromText(text: string): string[] {
  const isbns: string[] = [];
  
  // Try all patterns
  Object.values(ISBN_PATTERNS).forEach(pattern => {
    const matches = text.match(pattern);
    if (matches) {
      isbns.push(...matches.map(match => cleanISBN(match)));
    }
  });

  // Remove duplicates and filter valid ISBNs
  const uniqueISBNs = [...new Set(isbns)];
  return uniqueISBNs.filter(isbn => 
    validateISBN10(isbn) || validateISBN13(isbn)
  );
}

/**
 * Detect ISBN from product name, description, or explicit field
 */
export function detectISBN(product: {
  name: string;
  description: string;
  isbn?: string;
}): ISBNInfo {
  let detectedISBNs: string[] = [];
  let detectedFrom: ISBNInfo['detectedFrom'] = 'manual';

  // First check explicit ISBN field
  if (product.isbn) {
    const cleanedISBN = cleanISBN(product.isbn);
    if (validateISBN10(cleanedISBN) || validateISBN13(cleanedISBN)) {
      detectedISBNs.push(cleanedISBN);
      detectedFrom = 'field';
    }
  }

  // If no valid ISBN in field, try extracting from name
  if (detectedISBNs.length === 0) {
    const nameISBNs = extractISBNsFromText(product.name);
    if (nameISBNs.length > 0) {
      detectedISBNs = nameISBNs;
      detectedFrom = 'name';
    }
  }

  // If still no ISBN, try description
  if (detectedISBNs.length === 0) {
    const descISBNs = extractISBNsFromText(product.description);
    if (descISBNs.length > 0) {
      detectedISBNs = descISBNs;
      detectedFrom = 'description';
    }
  }

  if (detectedISBNs.length === 0) {
    return { isValid: false, detectedFrom: 'manual' };
  }

  // Use the first detected ISBN
  const primaryISBN = detectedISBNs[0];
  const result: ISBNInfo = {
    isValid: true,
    detectedFrom
  };

  // Determine if it's ISBN-10 or ISBN-13 and convert if needed
  if (primaryISBN.length === 10) {
    result.isbn10 = primaryISBN;
    const isbn13 = isbn10ToISBN13(primaryISBN);
    if (isbn13) result.isbn13 = isbn13;
  } else if (primaryISBN.length === 13) {
    result.isbn13 = primaryISBN;
    const isbn10 = isbn13ToISBN10(primaryISBN);
    if (isbn10) result.isbn10 = isbn10;
  }

  return result;
}

/**
 * Format ISBN for display
 */
export function formatISBN(isbn: string, type: 'isbn10' | 'isbn13' = 'isbn13'): string {
  const cleaned = cleanISBN(isbn);
  
  if (type === 'isbn10' && cleaned.length === 10) {
    return `${cleaned.substring(0, 1)}-${cleaned.substring(1, 6)}-${cleaned.substring(6, 8)}-${cleaned.substring(8)}`;
  } else if (type === 'isbn13' && cleaned.length === 13) {
    return `${cleaned.substring(0, 3)}-${cleaned.substring(3, 4)}-${cleaned.substring(4, 9)}-${cleaned.substring(9, 12)}-${cleaned.substring(12)}`;
  }
  
  return cleaned;
}

/**
 * Check if a string contains any ISBN
 */
export function containsISBN(text: string): boolean {
  return extractISBNsFromText(text).length > 0;
}
