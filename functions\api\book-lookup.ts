/**
 * API endpoint: /api/book-lookup?olid=OLXXXXXXXXX
 * Looks up book data by Open Library ID (OLID) using Open Library API.
 */
import { lookupBookByOLID, searchBooksByName } from '../../src/services/bookLookupService';

export async function fetch(request: Request, env: unknown, ctx: unknown): Promise<Response> {
  const url = new URL(request.url);
  const olid = url.searchParams.get('olid');
  const name = url.searchParams.get('name');

  if (name && typeof name === 'string') {
    // Book search by name
    try {
      const result = await searchBooksByName(name);
      if (result.success) {
        // If books found, return them; if not, return empty array with 200
        return new Response(
          JSON.stringify({ success: true, books: result.books || [] }),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        );
      } else if (result.error) {
        // Only return 404 if there is an actual error from the API/service
        return new Response(
          JSON.stringify({ success: false, error: result.error }),
          { status: 404, headers: { 'Content-Type': 'application/json' } }
        );
      } else {
        // Fallback: treat as no results
        return new Response(
          JSON.stringify({ success: true, books: [] }),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify({ success: false, error: error instanceof Error ? error.message : 'Internal server error.' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
  } else if (olid && typeof olid === 'string') {
    // Book lookup by OLID (legacy)
    try {
      const result = await lookupBookByOLID(olid);
      if (result.success) {
        return new Response(
          JSON.stringify(result.bookData),
          { status: 200, headers: { 'Content-Type': 'application/json' } }
        );
      } else {
        return new Response(
          JSON.stringify({ success: false, error: result.error || 'Book not found.' }),
          { status: 404, headers: { 'Content-Type': 'application/json' } }
        );
      }
    } catch (error) {
      return new Response(
        JSON.stringify({ success: false, error: error instanceof Error ? error.message : 'Internal server error.' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
  } else {
    return new Response(
      JSON.stringify({ success: false, error: 'Missing or invalid "name" or "olid" query parameter.' }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    );
  }
}