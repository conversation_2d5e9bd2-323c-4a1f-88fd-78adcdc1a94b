.admin-products-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

.admin-products-table th,
.admin-products-table td {
  box-sizing: border-box;
  padding: 0.75rem;
  text-align: left;
  vertical-align: middle;
}
/* Admin Panel Styles */

/* Modern Admin Panel Specific Styles */

/* Input Help Styles */
.input-help {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.375rem;
  font-size: 0.8125rem;
  color: var(--text-secondary);
}

.condition-guide-link {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.condition-guide-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}
.admin-tab-btn {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.admin-tab-btn:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.admin-tab-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-sync {
  background: #16a34a;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-sync:hover {
  background: #15803d;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-sync:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.btn-secondary:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.btn-back-home {
  background: var(--primary);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-back-home:hover {
  background: var(--primary-dark);
  color: white;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Admin Panel Layout Improvements - COMPACT VERSION */
.admin-section {
  width: 100%;
  max-width: none;
  padding: 0;
  margin: 0 auto 1rem auto;
  display: none;
}

.admin-section.active {
  display: block;
}

/* Admin Controls and Filters - COMPACT */
.admin-controls {
  margin-bottom: 1rem;
}

.admin-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.filter-group label {
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.search-group {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper svg {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  pointer-events: none;
}

.admin-select,
.admin-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
}

.admin-input[type="search"] {
  padding-left: 2.5rem;
}

.admin-select:focus,
.admin-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* State containers */
.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.admin-sidebar {
  min-width: 200px;
  max-width: 240px;
  width: 20%;
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.admin-table th {
  background: var(--border-light);
  font-weight: 600;
  border-bottom: 2px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 2;
  color: var(--text);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 0.75rem;
}

.admin-table td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.admin-table tr:hover {
  background: var(--border-light);
}

/* Modern Admin Panel Styles */
.access-denied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
  background: var(--light-background);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border);
  margin: 2rem auto;
  max-width: 600px;
}

.access-denied-icon {
  font-size: 4rem;
  color: var(--muted);
  margin-bottom: 1.5rem;
}

.access-denied h1 {
  color: var(--text);
  margin-bottom: 1rem;
  font-size: 2rem;
}

.access-denied p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.125rem;
  line-height: 1.6;
}

.admin-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  background: var(--background);
  min-height: 100vh;
}

/* COMPACT INTEGRATED ADMIN HEADER */
.compact-admin-header {
  background: var(--light-background);
  border-radius: var(--radius);
  padding: 0.75rem 1rem;
  margin-bottom: 0.75rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-light);
}

.admin-title-compact {
  display: flex;
  align-items: baseline;
  gap: 0.75rem;
}

.admin-title-compact h1 {
  margin: 0;
  color: var(--text);
  font-size: 1.25rem;
  font-weight: 700;
}

.admin-title-compact .subtitle {
  color: var(--text-secondary);
  font-size: 0.8rem;
  font-weight: 500;
}

.admin-actions-compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-bottom-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.admin-nav-compact {
  display: flex;
  gap: 0.25rem;
}

.admin-tab-btn-compact {
  background: var(--background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
}

.admin-tab-btn-compact:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.admin-tab-btn-compact.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.admin-stats-inline {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.stat-item-inline {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--background);
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.stat-number-inline {
  font-size: 1rem;
  font-weight: 700;
  color: var(--primary);
}

.stat-label-inline {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-filters-compact {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.admin-select-compact {
  padding: 0.375rem 0.5rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.75rem;
  background: var(--background);
  color: var(--text);
  min-width: 120px;
}

.search-input-wrapper-compact {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper-compact svg {
  position: absolute;
  left: 0.5rem;
  color: var(--text-secondary);
  z-index: 1;
}

.admin-input-compact {
  padding: 0.375rem 0.5rem 0.375rem 2rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.75rem;
  background: var(--background);
  color: var(--text);
  width: 150px;
}

/* Compact Button Styles */
.btn-sync-compact,
.btn-secondary-compact,
.btn-primary-compact {
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  border: none;
}

.btn-sync-compact {
  background: #16a34a;
  color: white;
}

.btn-sync-compact:hover {
  background: #15803d;
}

.btn-secondary-compact {
  background: var(--background);
  color: var(--text);
  border: 1px solid var(--border);
}

.btn-secondary-compact:hover {
  background: var(--border-light);
}

.btn-primary-compact {
  background: var(--primary);
  color: white;
}

.btn-primary-compact:hover {
  background: var(--primary-dark);
}

/* COMPACT FORM STYLES */
.compact-form-wrapper {
  background: var(--light-background);
  border-radius: var(--radius);
  padding: 1rem;
  border: 1px solid var(--border);
}

.compact-form-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-section {
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1rem;
}

.section-title {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.5rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--background);
  color: var(--text);
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.price-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.price-wrapper .currency {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
  z-index: 1;
}

.price-wrapper input {
  padding-left: 1.75rem;
}

.help-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.help-text.warning {
  color: #f59e0b;
}

.required {
  color: #ef4444;
}

/* Features Section */
.features-area {
  min-height: 60px;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.5rem;
  background: var(--light-background);
  margin-bottom: 0.5rem;
}

.feature-input {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 0.5rem;
  align-items: end;
}

.btn-add-feature {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s ease;
}

.btn-add-feature:hover {
  background: var(--primary-dark);
}

/* Feature Display */
.feature-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0.5rem;
  background: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-content {
  display: flex;
  gap: 0.5rem;
}

.feature-label {
  font-weight: 600;
  color: var(--text);
}

.feature-value {
  color: var(--text-secondary);
}

.feature-remove {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: 0.125rem 0.375rem;
  cursor: pointer;
  font-size: 0.75rem;
  transition: background-color 0.2s ease;
}

.feature-remove:hover {
  background: #dc2626;
}

/* Mobile Responsiveness for Compact Design */
@media (max-width: 768px) {
  .compact-admin-header {
    padding: 0.5rem;
  }

  .header-top-row {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .admin-title-compact {
    justify-content: center;
    text-align: center;
  }

  .admin-actions-compact {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.375rem;
  }

  .header-bottom-row {
    flex-direction: column;
    gap: 0.75rem;
  }

  .admin-nav-compact {
    justify-content: center;
    flex-wrap: wrap;
  }

  .admin-stats-inline {
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .admin-filters-compact {
    flex-direction: column;
    gap: 0.5rem;
  }

  .admin-select-compact,
  .admin-input-compact {
    width: 100%;
    min-width: auto;
  }

  /* Compact Form Mobile */
  .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .feature-input {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .btn-add-feature {
    justify-self: start;
    width: fit-content;
  }
}

.admin-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.admin-nav-item {
  background: var(--light-background);
  color: var(--text);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--border);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.875rem;
}

.admin-nav-item:hover {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin-nav-item.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.admin-card {
  background: var(--light-background);
  border-radius: var(--radius-xl);
  padding: 2rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  margin-bottom: 2rem;
}

.admin-card h2 {
  margin: 0 0 1rem 0;
  color: var(--text);
  font-size: 1.5rem;
  font-weight: 600;
}

.admin-card h3 {
  margin: 0 0 0.75rem 0;
  color: var(--text);
  font-size: 1.25rem;
  font-weight: 600;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.admin-stat-card {
  background: var(--light-background);
  border-radius: var(--radius);
  padding: 0.75rem;
  border: 1px solid var(--border);
  text-align: center;
  transition: all 0.2s ease;
}

.admin-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.admin-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.5rem;
  display: block;
}

.admin-stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.admin-btn {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.admin-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.admin-btn.secondary {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
}

.admin-btn.secondary:hover {
  background: var(--border-light);
  color: var(--text);
}

.admin-btn.danger {
  background: #dc2626;
}

.admin-btn.danger:hover {
  background: #b91c1c;
}

.admin-form {
  display: grid;
  gap: 1.5rem;
}

.admin-form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.admin-form-label {
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.admin-form-input,
.admin-form-textarea,
.admin-form-select {
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
}

.admin-form-input:focus,
.admin-form-textarea:focus,
.admin-form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.admin-form-textarea {
  min-height: 120px;
  resize: vertical;
}

.admin-alert {
  padding: 1rem 1.5rem;
  border-radius: var(--radius);
  margin-bottom: 1.5rem;
  border: 1px solid;
}

.admin-alert.success {
  background: #f0fdf4;
  border-color: #22c55e;
  color: #15803d;
}

.admin-alert.error {
  background: #fef2f2;
  border-color: #ef4444;
  color: #dc2626;
}

.admin-alert.warning {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #d97706;
}

.admin-alert.info {
  background: #fef3c7;
  border-color: #d97706;
  color: #92400e;
}

/* Loading states */
.admin-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.admin-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Professional Form Styles */
.professional-form-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

.form-header-section {
  background: var(--light-background);
  border-radius: var(--radius);
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.form-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.form-header-icon {
  background: var(--primary);
  color: white;
  padding: 0.75rem;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-header-text h2 {
  margin: 0 0 0.25rem 0;
  color: var(--text);
  font-size: 1.25rem;
  font-weight: 700;
}

.form-header-text p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.form-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-card {
  background: var(--light-background);
  border-radius: var(--radius);
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.form-card.primary-card {
  grid-column: 1 / -1;
}

.card-header {
  background: var(--border-light);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-icon {
  padding: 0.75rem;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon.primary-icon {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary);
}

.card-icon.secondary-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.card-icon.features-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.card-icon.notes-icon {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.card-title h3 {
  margin: 0 0 0.25rem 0;
  color: var(--text);
  font-size: 1.25rem;
  font-weight: 600;
}

.card-title p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.card-content {
  padding: 1rem;
}

.input-group {
  margin-bottom: 1rem;
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text);
  font-size: 0.875rem;
}

.required {
  color: #dc2626;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.875rem;
  background: var(--light-background);
  color: var(--text);
  transition: all 0.2s ease;
  font-family: inherit;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.input-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.price-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.currency-prefix {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  pointer-events: none;
}

.price-input {
  padding-left: 2rem;
}

.input-help {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.warning-help {
  color: #d97706;
}

.image-upload-area {
  position: relative;
}

.image-textarea {
  font-family: 'Courier New', monospace;
  font-size: 0.8125rem;
}

/* Features Section */
.features-display-area {
  margin-bottom: 1.5rem;
}

.keypoint-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--border-light);
  border-radius: var(--radius);
  margin-bottom: 0.5rem;
  border: 1px solid var(--border);
}

.keypoint-label {
  font-weight: 600;
  color: var(--text);
  margin-right: 0.5rem;
}

.keypoint-value {
  color: var(--text-secondary);
  flex: 1;
}

.btn-remove-keypoint {
  background: #dc2626;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  line-height: 1;
  transition: all 0.2s ease;
}

.btn-remove-keypoint:hover {
  background: #b91c1c;
  transform: scale(1.1);
}

.features-input-section {
  border-top: 1px solid var(--border);
  padding-top: 1.5rem;
}

.features-input-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-feature-button {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.add-feature-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Pagination */
.admin-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  padding: 1rem;
}

.page-btn {
  background: var(--light-background);
  color: var(--text);
  border: 1px solid var(--border);
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  min-width: 40px;
  text-align: center;
}

.page-btn:hover {
  background: var(--border-light);
  border-color: var(--primary);
}

.page-btn.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* GitHub Status */
.github-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--border-light);
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.status-indicator {
  font-size: 0.75rem;
}

.status-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Categories List - Compact Design */
.categories-grid {
  /* Container for the new list layout */
}

.categories-list {
  background: white;
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-top: 1rem;
}

.categories-list-header {
  display: grid;
  grid-template-columns: 2fr auto auto auto;
  gap: 2rem;
  padding: 1rem 1.5rem;
  background: var(--light-background);
  border-bottom: 1px solid var(--border);
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.categories-list-body {
  /* Container for category items */
}

.category-list-item {
  display: grid;
  grid-template-columns: 2fr auto auto auto;
  gap: 2rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border);
  transition: background-color 0.2s ease;
  align-items: center;
}

.category-list-item:last-child {
  border-bottom: none;
}

.category-list-item:hover {
  background: var(--light-background);
}

.category-name-col {
  display: flex;
  align-items: center;
}

.category-name {
  font-weight: 500;
  color: var(--text);
  font-size: 0.95rem;
}

.category-count-col {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.product-count {
  background: var(--light-background);
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 24px;
  text-align: center;
}

.category-status-col {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.status-active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.status-ready {
  background: #fef3c7;
  color: #92400e;
}

.category-actions-col {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80px;
}

.category-actions {
  display: flex;
  gap: 0.25rem;
}

.btn-edit-category,
.btn-delete-category {
  padding: 0.375rem;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.btn-edit-category {
  background: var(--primary);
  color: white;
}

.btn-edit-category:hover {
  background: var(--primary-dark);
  transform: scale(1.05);
}

.btn-delete-category {
  background: #dc2626;
  color: white;
}

.btn-delete-category:hover {
  background: #b91c1c;
  transform: scale(1.05);
}

/* No Categories State */
.no-categories {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--text-secondary);
  background: white;
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  margin-top: 1rem;
  font-size: 0.95rem;
}


.no-categories {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 2rem;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  align-items: center;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border);
}

.form-header h2 {
  margin: 0;
  color: var(--text);
  font-size: 1.5rem;
  font-weight: 600;
}

/* Stat Cards - COMPACT */
.stat-card {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 0.75rem;
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.25rem;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Admin Products Table - COMPACT */
.admin-table-wrapper {
  background: var(--light-background);
  border-radius: var(--radius);
  border: 1px solid var(--border);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.admin-table,
.admin-products-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

/* Product table specific styles */
.product-row {
  transition: background-color 0.2s ease;
}

.product-row:hover {
  background: var(--border-light);
}

.product-row.product-new {
  background: rgba(34, 197, 94, 0.1);
  border-left: 3px solid #22c55e;
}

.product-row.product-modified {
  background: rgba(245, 158, 11, 0.1);
  border-left: 3px solid #f59e0b;
}

.product-name-cell {
  max-width: 300px;
}

.product-name-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.product-description-preview {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.3;
}

.change-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.change-indicator.new {
  color: #22c55e;
}

.change-indicator.modified {
  color: #f59e0b;
}

.category-badge {
  background: var(--border-light);
  color: var(--text-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
}

.images-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.good {
  background: #f0fdf4;
  color: #15803d;
  border: 1px solid #22c55e;
}

.status-badge.defects {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #f59e0b;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-edit,
.btn-delete {
  padding: 0.375rem;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-edit {
  background: var(--primary);
  color: white;
}

.btn-edit:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.btn-delete {
  background: #dc2626;
  color: white;
}

.btn-delete:hover {
  background: #b91c1c;
  transform: translateY(-1px);
}

.admin-table thead {
  background: var(--border-light);
}

.admin-table th {
  padding: 0.5rem 0.5rem;
  text-align: left;
  font-weight: 600;
  color: var(--text);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 10;
  font-size: 0.875rem;
}

.admin-table td {
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
  font-size: 0.875rem;
}

.admin-table tr:hover {
  background: var(--border-light);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.admin-table-btn {
  padding: 0.375rem 0.75rem;
  border: none;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.admin-table-btn.edit {
  background: var(--primary);
  color: white;
}

.admin-table-btn.edit:hover {
  background: var(--primary-dark);
}

.admin-table-btn.delete {
  background: #dc2626;
  color: white;
}

.admin-table-btn.delete:hover {
  background: #b91c1c;
}

.admin-table-btn.view {
  background: var(--border-light);
  color: var(--text);
  border: 1px solid var(--border);
}

.admin-table-btn.view:hover {
  background: var(--border);
}

.product-image-thumb {
  width: 60px;
  height: 45px;
  object-fit: cover;
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.product-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.product-status.available {
  background: #f0fdf4;
  color: #15803d;
  border: 1px solid #22c55e;
}

.product-status.sold {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

.product-status.pending {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #f59e0b;
}

/* Categories Management */
.categories-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* When form is visible, use a two-column layout */
.categories-container:has(#category-form-container:not([style*="display: none"])) {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

/* Fallback for browsers that don't support :has() */
.categories-container.form-visible {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  align-items: start;
}

.category-form {
  background: var(--light-background);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

/* Duplicate styling removed - using the main .categories-list styling above */

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-light);
}

.category-item:last-child {
  border-bottom: none;
}

.category-item:hover {
  background: var(--border-light);
}

.category-name {
  font-weight: 500;
  color: var(--text);
}

.category-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

.category-actions {
  display: flex;
  gap: 0.5rem;
}

/* Mobile Responsive for Admin Panel */
@media (max-width: 768px) {
  .admin-container {
    padding: 0.5rem;
  }

  .admin-header {
    padding: 0.75rem 1rem;
  }

  .admin-header h1 {
    font-size: 1.5rem;
  }

  .admin-nav {
    flex-direction: column;
    gap: 0.25rem;
  }

  .admin-nav-item {
    justify-content: center;
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .admin-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .stat-card {
    padding: 0.5rem;
  }

  .stat-number {
    font-size: 1.25rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }

  .admin-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-btn {
    justify-content: center;
  }

  .categories-container,
  .categories-container.form-visible,
  .categories-container:has(#category-form-container:not([style*="display: none"])) {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .admin-table-wrapper {
    overflow-x: auto;
  }

  .admin-table {
    min-width: 600px;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.375rem;
    font-size: 0.75rem;
  }

  .admin-table-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .admin-table-btn {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
  }

  .product-image-thumb {
    width: 35px;
    height: 25px;
  }

  .admin-filters {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .filter-group {
    gap: 0.25rem;
  }

  .form-content-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

/* Image Upload Styles */
.image-upload-container {
  margin-bottom: 1rem;
}

.upload-area {
  border: 2px dashed var(--border);
  border-radius: var(--radius);
  padding: 2rem;
  text-align: center;
  background: var(--light-background);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: var(--primary);
  background: var(--primary-light);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.upload-content svg {
  color: var(--text-secondary);
  opacity: 0.7;
}

.upload-content h4 {
  margin: 0;
  color: var(--text);
  font-size: 1.125rem;
  font-weight: 600;
}

.upload-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.btn-upload {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.btn-upload:hover {
  background: var(--primary-dark);
}

.upload-progress {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--light-background);
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-light);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: var(--primary);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.image-previews {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.image-preview {
  position: relative;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  overflow: hidden;
  background: white;
}

.image-preview img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.preview-info {
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.25rem;
}

.preview-filename {
  font-size: 0.75rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.preview-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-set-featured {
  background: var(--border);
  color: var(--text-secondary);
  border: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.btn-set-featured:hover {
  background: var(--primary);
  color: white;
  transform: scale(1.1);
}

.btn-set-featured.active {
  background: var(--primary);
  color: white;
}

.btn-set-featured svg {
  width: 12px;
  height: 12px;
}

.btn-remove-image {
  background: var(--danger);
  color: white;
  border: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  line-height: 1;
  transition: background 0.2s ease;
  flex-shrink: 0;
}

.btn-remove-image:hover {
  background: var(--danger-dark);
}

/* Featured image styling */
.image-preview.featured {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.image-preview.featured::before {
  content: "FEATURED";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: var(--primary);
  color: white;
  font-size: 0.625rem;
  font-weight: 600;
  text-align: center;
  padding: 0.125rem;
  z-index: 1;
}

.manual-url-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-light);
}

.manual-url-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text);
}

/* Responsive adjustments for image upload */
@media (max-width: 768px) {
  .upload-area {
    padding: 1.5rem 1rem;
  }

  .upload-content h4 {
    font-size: 1rem;
  }

  .upload-content p {
    font-size: 0.8125rem;
  }

  .image-previews {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
  }

  .image-preview img {
    height: 60px;
  }
}

/* Admin Controls - Items per page and pagination info */
.admin-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  gap: 1rem;
  flex-wrap: wrap;
}

.items-per-page-admin {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.admin-items-per-page-select {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background: white;
  font-size: 0.875rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
  min-width: 80px;
}

.admin-items-per-page-select:hover {
  border-color: var(--primary);
}

.admin-items-per-page-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.admin-pagination-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

@media (max-width: 768px) {
  .admin-controls {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .items-per-page-admin {
    justify-content: center;
  }

  .admin-pagination-info {
    text-align: center;
  }
}

/* Category Management - Unused Categories Styling */
.category-list-item.category-unused {
  background: #fef3c7;
  border-left: 3px solid #f59e0b;
}

.category-list-item.category-unused:hover {
  background: #fde68a;
}

.category-list-item.category-unused .category-name {
  color: #d97706;
  font-weight: 600;
}

.category-list-item.category-unused .product-count {
  background: #f59e0b;
  color: white;
}

/* Responsive Categories List */
@media (max-width: 768px) {
  .categories-list-header {
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.75rem 1rem;
  }

  .category-list-item {
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.75rem 1rem;
  }

  .category-status-col {
    display: none; /* Hide status on mobile to save space */
  }

  .categories-list-header .category-status-col {
    display: none;
  }

  .category-actions {
    gap: 0.125rem;
  }

  .btn-edit-category,
  .btn-delete-category {
    width: 28px;
    height: 28px;
    padding: 0.25rem;
  }

  .category-name {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .categories-list-header {
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  .category-list-item {
    padding: 0.5rem;
  }

  .category-name {
    font-size: 0.85rem;
  }

  .product-count {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Book-specific form styles */
.book-fields-section {
  background: var(--light-background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.book-section-title {
  color: var(--primary);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.book-section-title::before {
  content: "📚";
  font-size: 1.25rem;
}

.btn-lookup {
  background: var(--primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
  white-space: nowrap;
}

.btn-lookup:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

.btn-lookup:disabled {
  background: var(--border);
  cursor: not-allowed;
  transform: none;
}

.book-details {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
}

.book-details .form-group input {
  background: var(--light-background);
  border: 1px solid var(--border);
  color: var(--text-secondary);
}

.book-details .form-group input[readonly] {
  cursor: default;
}

.book-details .form-group label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* Book lookup status indicators */
.book-lookup-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

.book-lookup-status.success {
  color: #10b981;
}

.book-lookup-status.error {
  color: #ef4444;
}

.book-lookup-status.loading {
  color: var(--text-secondary);
}

/* Responsive adjustments for book fields */
@media (max-width: 768px) {
  .book-fields-section {
    padding: 1rem;
    margin: 1rem 0;
  }

  .btn-lookup {
    width: 100%;
    margin-top: 0.75rem;
  }

  .book-details .form-row {
    flex-direction: column;
  }

  .book-details .form-group {
    margin-bottom: 1rem;
  }
}
