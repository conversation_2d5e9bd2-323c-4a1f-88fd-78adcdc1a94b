/**
 * Client-side Image Resizing Utility
 * Generates multiple image variants for optimal performance
 */

export class ImageResizer {
  constructor() {
    // Define image variants - now using max dimensions to preserve aspect ratios
    this.variants = {
      thumbnail: { maxWidth: 150, maxHeight: 150, suffix: '-thumb', quality: 0.8 },
      mobile: { maxWidth: 480, maxHeight: 480, suffix: '-mobile', quality: 0.85 },
      tablet: { maxWidth: 768, maxHeight: 768, suffix: '-tablet', quality: 0.85 },
      desktop: { width: null, height: null, suffix: '-desktop', quality: 0.9 }, // Preserve original size
      original: { width: null, height: null, suffix: '-original', quality: 0.95 } // Preserve original size
    };
  }

  /**
   * Resize a single image file to all variants
   * @param {File} file - The original image file
   * @returns {Promise<Object>} - Object containing all resized variants
   */
  async resizeImage(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      img.onload = async () => {
        try {
          const variants = {};
          const baseFilename = this.getBaseFilename(file.name);
          
          // Generate each variant
          for (const [variantName, config] of Object.entries(this.variants)) {
            const resizedBlob = await this.createVariant(img, canvas, ctx, config);
            const filename = `${baseFilename}${config.suffix}.webp`;

            // Calculate actual dimensions based on config
            let actualWidth, actualHeight;
            if (config.width === null || config.height === null) {
              // Preserve original dimensions
              actualWidth = img.width;
              actualHeight = img.height;
            } else if (config.maxWidth && config.maxHeight) {
              // Calculate dimensions that fit within max bounds while preserving aspect ratio
              const aspectRatio = img.width / img.height;
              if (img.width > img.height) {
                actualWidth = Math.min(config.maxWidth, img.width);
                actualHeight = actualWidth / aspectRatio;
                if (actualHeight > config.maxHeight) {
                  actualHeight = config.maxHeight;
                  actualWidth = actualHeight * aspectRatio;
                }
              } else {
                actualHeight = Math.min(config.maxHeight, img.height);
                actualWidth = actualHeight * aspectRatio;
                if (actualWidth > config.maxWidth) {
                  actualWidth = config.maxWidth;
                  actualHeight = actualWidth / aspectRatio;
                }
              }
              actualWidth = Math.round(actualWidth);
              actualHeight = Math.round(actualHeight);
            } else {
              // Fixed dimensions (legacy)
              actualWidth = config.width;
              actualHeight = config.height;
            }

            variants[variantName] = {
              blob: resizedBlob,
              filename: filename,
              width: actualWidth,
              height: actualHeight,
              size: resizedBlob.size
            };
          }
          
          resolve({
            originalFilename: file.name,
            baseFilename: baseFilename,
            variants: variants,
            totalVariants: Object.keys(variants).length
          });
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error(`Failed to load image: ${file.name}`));
      };
      
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Create a single variant of the image
   * @param {HTMLImageElement} img - Source image
   * @param {HTMLCanvasElement} canvas - Canvas element
   * @param {CanvasRenderingContext2D} ctx - Canvas context
   * @param {Object} config - Variant configuration
   * @returns {Promise<Blob>} - Resized image blob
   */
  async createVariant(img, canvas, ctx, config) {
    // Calculate target dimensions
    let targetWidth, targetHeight;

    if (config.width === null || config.height === null) {
      // Preserve original dimensions
      targetWidth = img.width;
      targetHeight = img.height;
    } else if (config.maxWidth && config.maxHeight) {
      // Calculate dimensions that fit within max bounds while preserving aspect ratio
      const aspectRatio = img.width / img.height;
      if (img.width > img.height) {
        targetWidth = Math.min(config.maxWidth, img.width);
        targetHeight = targetWidth / aspectRatio;
        if (targetHeight > config.maxHeight) {
          targetHeight = config.maxHeight;
          targetWidth = targetHeight * aspectRatio;
        }
      } else {
        targetHeight = Math.min(config.maxHeight, img.height);
        targetWidth = targetHeight * aspectRatio;
        if (targetWidth > config.maxWidth) {
          targetWidth = config.maxWidth;
          targetHeight = targetWidth / aspectRatio;
        }
      }
      targetWidth = Math.round(targetWidth);
      targetHeight = Math.round(targetHeight);
    } else {
      // Fixed dimensions (legacy)
      targetWidth = config.width;
      targetHeight = config.height;
    }

    // Set canvas dimensions
    canvas.width = targetWidth;
    canvas.height = targetHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Draw the image scaled to fit (preserves aspect ratio, no cropping)
    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

    // Convert to blob with specified quality
    return new Promise((resolve) => {
      canvas.toBlob(resolve, 'image/webp', config.quality);
    });
  }

  /**
   * Process multiple files
   * @param {FileList|Array} files - Array of image files
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<Array>} - Array of processed image results
   */
  async processFiles(files, progressCallback = null) {
    const results = [];
    const totalFiles = files.length;
    
    for (let i = 0; i < totalFiles; i++) {
      const file = files[i];
      
      if (progressCallback) {
        progressCallback({
          current: i + 1,
          total: totalFiles,
          filename: file.name,
          stage: 'processing'
        });
      }
      
      try {
        const result = await this.resizeImage(file);
        results.push({
          success: true,
          originalFile: file,
          ...result
        });
      } catch (error) {
        results.push({
          success: false,
          originalFile: file,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * Get base filename without extension
   * @param {string} filename - Original filename
   * @returns {string} - Base filename
   */
  getBaseFilename(filename) {
    const timestamp = Date.now();
    const baseName = filename.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9-_]/g, '-');
    return `${baseName}-${timestamp}`;
  }

  /**
   * Calculate total size of all variants
   * @param {Object} variants - Variants object
   * @returns {number} - Total size in bytes
   */
  getTotalSize(variants) {
    return Object.values(variants).reduce((total, variant) => total + variant.size, 0);
  }

  /**
   * Format file size for display
   * @param {number} bytes - Size in bytes
   * @returns {string} - Formatted size string
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Validate image file
   * @param {File} file - File to validate
   * @returns {Object} - Validation result
   */
  validateFile(file) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      };
    }
    
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size too large. Maximum size is 10MB.'
      };
    }
    
    return { valid: true };
  }
}

// Export singleton instance
export const imageResizer = new ImageResizer();
