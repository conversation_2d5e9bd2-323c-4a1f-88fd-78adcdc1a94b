/**
 * Book Lookup Service using Open Library API (OLID only)
 * Fetches book details by OLID and transforms data to match our product schema
 */

import type { Product } from '../lib/products';

export interface BookLookupResult {
  success: boolean;
  bookData?: Partial<Product>;
  error?: string;
  source: 'open-library';
}

export interface BookSearchResult {
  success: boolean;
  books?: Array<{
    title: string;
    authors: string[];
    olid: string;
    first_publish_year?: number;
    cover_id?: number;
  }>;
  error?: string;
  source: 'open-library';
}

/**
 * Fetch book details from Open Library API by OLID (Book or Work)
 */
export async function lookupBookByOLID(olid: string): Promise<BookLookupResult> {
  // Clean OLID (remove whitespace)
  const cleanOLID = olid.trim();

  // OLID format validation: OLIDs are typically "OL" + digits + (optional letter) + "M" or "W"
  // Example: OL12345M, OL67890W
  const olidPattern = /^OL\d+[MW]$/i;
  if (!olidPattern.test(cleanOLID)) {
    return {
      success: false,
      error: `Invalid OLID format: "${olid}". OLID must match pattern "OL12345M" or "OL67890W".`,
      source: 'open-library'
    };
  }

  const endpoints = [
    `https://openlibrary.org/books/${cleanOLID}.json`,
    `https://openlibrary.org/works/${cleanOLID}.json`
  ];

  let olBook: any = null;
  let lastError: string | undefined = undefined;

  for (const url of endpoints) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        olBook = await response.json();
        break;
      } else if (response.status === 404) {
        // Not found, try next endpoint
        lastError = `No book found for OLID: ${cleanOLID}`;
      } else if (response.status >= 500 && response.status < 600) {
        // Server error
        lastError = `Open Library server error (${response.status}): ${response.statusText}`;
        break; // Don't try next endpoint if server error
      } else {
        // Other HTTP error
        lastError = `Open Library API error (${response.status}): ${response.statusText}`;
      }
    } catch (err) {
      // Network or fetch error
      lastError = err instanceof Error
        ? `Network error while contacting Open Library: ${err.message}`
        : 'Unknown network error while contacting Open Library';
      break; // Network error, don't try next endpoint
    }
  }

  if (!olBook) {
    return {
      success: false,
      error: lastError || `No book found for OLID: ${cleanOLID}`,
      source: 'open-library'
    };
  }

  const bookData = transformOpenLibraryData(olBook, cleanOLID);
  return {
    success: true,
    bookData,
    source: 'open-library'
  };
}

/**
 * Search for books by name using Open Library's /search.json?title= endpoint.
 */
export async function searchBooksByName(name: string): Promise<BookSearchResult> {
  const cleanName = name.trim();
  if (!cleanName) {
    return {
      success: false,
      error: 'Missing or empty book name.',
      source: 'open-library'
    };
  }

  const url = `https://openlibrary.org/search.json?title=${encodeURIComponent(cleanName)}`;
  try {
    const response = await fetch(url);
    if (!response.ok) {
      return {
        success: false,
        error: `Open Library API error (${response.status}): ${response.statusText}`,
        source: 'open-library'
      };
    }
    const data = await response.json();
    if (!Array.isArray(data.docs)) {
      return {
        success: false,
        error: 'Malformed response from Open Library.',
        source: 'open-library'
      };
    }
    const books = data.docs.slice(0, 20).map((doc: any) => ({
      title: doc.title || '',
      authors: Array.isArray(doc.author_name) ? doc.author_name : [],
      olid: Array.isArray(doc.edition_key) && doc.edition_key.length > 0 ? doc.edition_key[0] : '',
      first_publish_year: doc.first_publish_year,
      cover_id: doc.cover_i
    })).filter((b: any) => b.title && b.olid);

    return {
      success: true,
      books,
      source: 'open-library'
    };
  } catch (err) {
    return {
      success: false,
      error: err instanceof Error ? `Network error: ${err.message}` : 'Unknown error during Open Library lookup.',
      source: 'open-library'
    };
  }
}
  // Clean OLID (remove whitespace)

/**
 * Transform Open Library API data to our Product schema
 */
function transformOpenLibraryData(olBook: any, olid: string): Partial<Product> {
  // Authors
  let authors: string[] = [];
  if (Array.isArray(olBook.authors)) {
    // /books/OLID.json: authors is array of {key: "/authors/OL..."}
    // /works/OLID.json: authors is array of {author: {key: ...}}
    authors = olBook.authors
      .map((a: any) =>
        typeof a.name === 'string'
          ? a.name
          : a.author && typeof a.author.key === 'string'
            ? a.author.key
            : undefined
      )
      .filter(Boolean);
  }

  // Publisher
  let publisher: string | undefined = undefined;
  if (Array.isArray(olBook.publishers) && olBook.publishers.length > 0) {
    publisher = typeof olBook.publishers[0] === 'string'
      ? olBook.publishers[0]
      : olBook.publishers[0].name || undefined;
  }

  // Categories/Subjects
  const bookCategories = Array.isArray(olBook.subjects)
    ? olBook.subjects.map((s: any) => (typeof s === 'string' ? s : s.name || s)).filter(Boolean)
    : [];

  // Description
  let bookDescription: string | undefined = undefined;
  if (typeof olBook.description === 'string') {
    bookDescription = olBook.description;
  } else if (olBook.description && typeof olBook.description.value === 'string') {
    bookDescription = olBook.description.value;
  }

  // Thumbnail
  let bookThumbnail: string | undefined = undefined;
  if (olBook.covers && Array.isArray(olBook.covers) && olBook.covers.length > 0) {
    // Open Library cover image URL pattern
    bookThumbnail = `https://covers.openlibrary.org/b/id/${olBook.covers[0]}-L.jpg`;
  }

  // Published date
  let publishedDate: string | undefined = olBook.publish_date || olBook.first_publish_date;

  // Page count
  let pageCount: number | undefined = olBook.number_of_pages;

  // Language
  let language: string | undefined = undefined;
  if (Array.isArray(olBook.languages) && olBook.languages.length > 0) {
    const langKey = olBook.languages[0].key;
    if (typeof langKey === 'string' && langKey.length >= 4) {
      language = langKey.slice(-3); // e.g., "eng"
    }
  }
  // Title
  const title = olBook.title || '';

  return {
    authors,
    publisher,
    publishedDate,
    pageCount,
    language,
    bookCategories,
    bookDescription,
    bookThumbnail,
    name: title,
    description: bookDescription || '',
    category: 'Books'
  };
}
/**
 * Enhance existing product with book data
 */
export function enhanceProductWithBookData(
  product: Product, 
  bookData: Partial<Product>
): Product {
  return {
    ...product,
    ...bookData,
    // Preserve original product data that shouldn't be overwritten
    id: product.id,
    price: product.price,
    condition: product.condition,
    images: product.images,
    featuredImage: product.featuredImage,
    keyPoints: product.keyPoints,
    defects: product.defects,
    slug: product.slug,
    createdAt: product.createdAt,
    updatedAt: new Date().toISOString(),
    
    // Enhance name if book title is better
    name: bookData.name && bookData.name.length > product.name.length 
      ? bookData.name 
      : product.name,
      
    // Enhance description if book description is available and longer
    description: bookData.description && bookData.description.length > product.description.length
      ? bookData.description
      : product.description,
  };
}

/**
 * Check if a product is a book based on category or detected ISBN
 */
export function isBookProduct(product: Product): boolean {
  return (
    product.category.toLowerCase() === 'books' ||
    Boolean(product.isbn) ||
    Boolean(product.authors?.length) ||
    Boolean(product.publisher)
  );
}

