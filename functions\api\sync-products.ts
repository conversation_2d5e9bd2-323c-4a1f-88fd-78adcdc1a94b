// Cloudflare Pages Function for syncing products to GitHub
import { GitHubService } from '../../src/lib/github';
import { purgeProductCache } from '../../src/utils/cache';

interface Env {
  GITHUB_TOKEN: string;
  GITHUB_OWNER: string;
  GITHUB_REPO: string;
  CLOUDFLARE_ZONE_ID: string;
  CLOUDFLARE_API_TOKEN: string;
  CLOUDFLARE_BUILD_HOOK_URL: string;
}

export async function onRequestPost(context: any): Promise<Response> {
  const { request, env } = context;

  try {
    console.log('=== SYNC PRODUCTS API CALLED ===');

    // Parse request body
    const body = await request.json();
    const { products, previousCategories, createdCategories } = body;

    if (!products || !Array.isArray(products)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid products data'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log(`Syncing ${products.length} products...`);
    console.log(`Syncing ${createdCategories?.length || 0} created categories...`);

    // Check if categories have changed
    const productCategories = [...new Set(products.map(p => p.category))].filter(Boolean).sort();
    const allCurrentCategories = [...new Set([...productCategories, ...(createdCategories || [])])].sort();
    const categoriesChanged = previousCategories &&
      JSON.stringify(allCurrentCategories) !== JSON.stringify(previousCategories.sort());

    console.log('Product categories:', productCategories);
    console.log('Created categories:', createdCategories);
    console.log('All current categories:', allCurrentCategories);
    console.log('Previous categories:', previousCategories);
    console.log('Categories changed:', categoriesChanged);
    
    // Initialize GitHub service
    const github = new GitHubService({
      token: env.GITHUB_TOKEN,
      owner: env.GITHUB_OWNER,
      repo: env.GITHUB_REPO,
      branch: 'main'
    });
    
    // Commit to src/data/products.json only (public/data/products.json will be generated during build)
    console.log('Committing to GitHub...');

    const githubResult = await github.commitProductsAndCategories(products, allCurrentCategories);

    if (!githubResult.success) {
      console.error('GitHub commit failed:', githubResult.error);
      return new Response(JSON.stringify({
        success: false,
        error: `GitHub sync failed: ${githubResult.error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log('✅ GitHub commit successful');
    
    // Purge Cloudflare cache for affected URLs
    let cachePurged = false;
    if (env.CLOUDFLARE_ZONE_ID && env.CLOUDFLARE_API_TOKEN) {
      try {
        console.log('Purging Cloudflare cache...');
        const baseUrl = new URL(request.url).origin;
        const productSlugs = products.map(p => p.slug || p.id);

        const cacheResult = await purgeProductCache(
          env.CLOUDFLARE_ZONE_ID,
          env.CLOUDFLARE_API_TOKEN,
          baseUrl,
          productSlugs
        );

        cachePurged = cacheResult.success;
        if (cachePurged) {
          console.log('✅ Cache purged successfully');
        } else {
          console.warn('Cache purge failed:', cacheResult.error);
        }
      } catch (cacheError) {
        console.warn('Cache purge failed:', cacheError);
        // Don't fail the entire operation for cache issues
      }
    }
    
    // Build hook disabled - GitHub commits automatically trigger Cloudflare Pages builds
    // This prevents duplicate builds (one from GitHub commit + one from build hook)
    let buildTriggered = false;
    let buildMessage = 'GitHub commit will trigger automatic deployment';

    if (categoriesChanged) {
      console.log('ℹ️  Categories changed - GitHub commit will trigger automatic deployment with new category pages');
    } else {
      console.log('ℹ️  No category changes - GitHub commit will trigger standard deployment');
    }
    
    console.log('=== SYNC COMPLETED SUCCESSFULLY ===');
    
    return new Response(JSON.stringify({
      success: true,
      productsCount: products.length,
      githubCommit: true,
      commitSha: githubResult.commitSha,
      cachePurged,
      buildTriggered,
      categoriesChanged,
      currentCategories: allCurrentCategories,
      message: buildMessage
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('Sync products error:', error);
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return new Response(JSON.stringify({
      success: false,
      error: errorMessage
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle GET requests for status/info
export async function onRequestGet(context: any): Promise<Response> {
  return new Response(JSON.stringify({
    success: true,
    message: 'Sync Products API is available',
    methods: ['POST'],
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
}
