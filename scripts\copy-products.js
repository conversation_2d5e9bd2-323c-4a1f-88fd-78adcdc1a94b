#!/usr/bin/env node

/**
 * Copy products.json and admin scripts from src to public for runtime access
 * This ensures the admin panel and debug tools can access the products data and latest scripts
 */

import { readFileSync, writeFileSync, mkdirSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

try {
  console.log('📦 Copying data and scripts to public folder...');

  // 1. Copy products data
  const productsSrcPath = join(projectRoot, 'src/data/products.json');
  const productsDestPath = join(projectRoot, 'public/data/products.json');

  // Ensure destination directory exists
  mkdirSync(dirname(productsDestPath), { recursive: true });

  // Read and parse products data
  const productsData = readFileSync(productsSrcPath, 'utf8');
  const products = JSON.parse(productsData);

  console.log(`📊 Found ${products.length} products to copy`);

  // Write to public folder
  writeFileSync(productsDestPath, productsData);

  console.log('✅ Products data copied successfully');
  console.log(`   Source: ${productsSrcPath}`);
  console.log(`   Destination: ${productsDestPath}`);

  // 2. Copy admin panel script
  const adminSrcPath = join(projectRoot, 'src/scripts/admin-panel.js');
  const adminDestPath = join(projectRoot, 'public/scripts/admin-panel.js');

  // Ensure destination directory exists
  mkdirSync(dirname(adminDestPath), { recursive: true });

  // Copy admin script
  const adminScriptData = readFileSync(adminSrcPath, 'utf8');
  writeFileSync(adminDestPath, adminScriptData);

  console.log('✅ Admin script copied successfully');
  console.log(`   Source: ${adminSrcPath}`);
  console.log(`   Destination: ${adminDestPath}`);

} catch (error) {
  console.error('❌ Error copying files:', error.message);
  process.exit(1);
}
