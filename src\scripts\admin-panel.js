// Modern Admin Panel JavaScript - Extracted for better maintainability
class ModernAdminPanel {
  constructor(initialProducts, initialCategories = []) {
    this.products = [...initialProducts];
    this.filteredProducts = [...this.products];

    // Track manually created categories (even if no products use them yet)
    // Load from saved categories data
    this.createdCategories = new Set(initialCategories);

    this.categories = this.getCategories();
    this.currentPage = 1;
    this.itemsPerPage = 20;
    this.currentFilter = '';
    this.currentSearch = '';
    this.currentSort = 'newest';
    this.editingProduct = null;
    this.editingCategory = null;
    this.keyPoints = [];
    this.featuredImageUrl = null; // Track the featured image URL

    // Pagination properties
    this.itemsPerPage = 20; // Default items per page for admin

    // Track unsaved changes
    this.hasUnsavedChanges = false;
    this.lastSyncedProducts = [...initialProducts];
    this.lastSyncedCategories = [...new Set(initialProducts.map(p => p.category))].filter(Boolean).sort();

    this.init();
  }
  
  init() {
    this.bindEvents();
    this.renderStats();
    this.renderProducts();
    this.initializeForm();
    this.checkGitHubStatus();
    this.updateSyncButtonState();
  }

  bindEvents() {
    // Tab navigation
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = e.target.closest('.admin-tab-btn').dataset.tab;
        this.switchTab(tab);
      }, { passive: true });
    });

    // Filters and search
    document.getElementById('admin-category-filter').addEventListener('change', (e) => {
      this.currentFilter = e.target.value;
      this.currentPage = 1;
      this.filterAndRenderProducts();
    }, { passive: true });

    document.getElementById('admin-sort-by').addEventListener('change', (e) => {
      this.currentSort = e.target.value;
      this.filterAndRenderProducts();
    }, { passive: true });

    document.getElementById('admin-search').addEventListener('input', (e) => {
      this.currentSearch = e.target.value.toLowerCase();
      this.currentPage = 1;
      this.filterAndRenderProducts();
    }, { passive: true });

    // Product actions
    document.getElementById('add-product-btn').addEventListener('click', () => {
      this.editingProduct = null;
      this.switchTab('form');
      this.resetForm();
    });

    document.getElementById('save-product').addEventListener('click', () => {
      this.saveProduct();
    });

    document.getElementById('cancel-form').addEventListener('click', () => {
      this.switchTab('list');
      this.resetForm();
    });

    // Category actions
    document.getElementById('add-category-btn').addEventListener('click', () => {
      this.showCategoryForm();
    });

    document.getElementById('save-category').addEventListener('click', () => {
      this.saveCategory();
    });

    document.getElementById('cancel-category').addEventListener('click', () => {
      this.hideCategoryForm();
    });

    // Sync actions
    document.getElementById('sync-products').addEventListener('click', () => {
      this.syncProducts();
    });

    document.getElementById('test-github').addEventListener('click', () => {
      this.testGitHub();
    });

    // Items per page selector
    const itemsPerPageSelect = document.getElementById('admin-items-per-page');
    if (itemsPerPageSelect) {
      itemsPerPageSelect.addEventListener('change', (e) => {
        this.itemsPerPage = parseInt(e.target.value);
        this.currentPage = 1; // Reset to first page
        this.renderProducts();
      });
    }
  }

  switchTab(tab) {
    // Clean up any existing event listeners before switching
    this.cleanupEventListeners();

    // Update tab buttons
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

    // Update sections
    document.querySelectorAll('.admin-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(`section-${tab}`).classList.add('active');

    if (tab === 'form') {
      this.initializeForm();
    } else if (tab === 'categories') {
      this.renderCategories();
    }

    // Re-bind events for the new tab
    this.bindTabSpecificEvents(tab);
  }

  // Clean up event listeners to prevent memory leaks
  cleanupEventListeners() {
    // Remove dynamically created event listeners
    const removeButtons = document.querySelectorAll('.btn-remove-keypoint');
    removeButtons.forEach(btn => {
      const newBtn = btn.cloneNode(true);
      btn.parentNode.replaceChild(newBtn, btn);
    });

    const imageRemoveButtons = document.querySelectorAll('.btn-remove-image');
    imageRemoveButtons.forEach(btn => {
      const newBtn = btn.cloneNode(true);
      btn.parentNode.replaceChild(newBtn, btn);
    });

    const featuredButtons = document.querySelectorAll('.btn-set-featured');
    featuredButtons.forEach(btn => {
      const newBtn = btn.cloneNode(true);
      btn.parentNode.replaceChild(newBtn, btn);
    });

    // Clean up file input event listeners to prevent duplicates
    const fileInput = document.getElementById('image-file-input');
    if (fileInput) {
      const newFileInput = fileInput.cloneNode(true);
      fileInput.parentNode.replaceChild(newFileInput, fileInput);
    }

    // Clean up upload area event listeners
    const uploadArea = document.getElementById('image-upload-area');
    if (uploadArea) {
      const newUploadArea = uploadArea.cloneNode(true);
      uploadArea.parentNode.replaceChild(newUploadArea, uploadArea);
    }
  }

  // Bind events specific to each tab
  bindTabSpecificEvents(tab) {
    if (tab === 'form') {
      this.bindFormEvents();
    }
  }

  getCategories() {
    // Get categories from products
    const productCategories = [...new Set(this.products.map(p => p.category))].filter(Boolean);

    // Combine with manually created categories
    const allCategories = [...new Set([...productCategories, ...this.createdCategories])];

    return allCategories.sort();
  }

  renderStats() {
    document.getElementById('total-products').textContent = this.products.length;
    document.getElementById('visible-products').textContent = this.filteredProducts.length;
    document.getElementById('categories-count').textContent = this.categories.length;
  }

  filterAndRenderProducts() {
    let filtered = [...this.products];

    // Apply category filter
    if (this.currentFilter) {
      filtered = filtered.filter(p => p.category === this.currentFilter);
    }

    // Apply search filter
    if (this.currentSearch) {
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(this.currentSearch) ||
        p.description.toLowerCase().includes(this.currentSearch) ||
        p.category.toLowerCase().includes(this.currentSearch)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.currentSort) {
        case 'price':
          return parseFloat(a.price) - parseFloat(b.price);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'newest':
          return new Date(b.dateAdded || 0) - new Date(a.dateAdded || 0);
        default: // name
          return a.name.localeCompare(b.name);
      }
    });

    this.filteredProducts = filtered;
    this.renderProducts();
    this.renderStats();
  }

  renderProducts() {
    const tbody = document.getElementById('products-tbody');
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

    if (pageProducts.length === 0) {
      document.getElementById('empty-state').style.display = 'block';
      document.querySelector('.admin-table-wrapper').style.display = 'none';
      document.getElementById('pagination').style.display = 'none';
    } else {
      document.getElementById('empty-state').style.display = 'none';
      document.querySelector('.admin-table-wrapper').style.display = 'block';
      document.getElementById('pagination').style.display = 'flex';
    }

    tbody.innerHTML = pageProducts.map(product => this.renderProductRow(product)).join('');
    this.renderPagination();
  }

  renderProductRow(product) {
    const isNew = !this.lastSyncedProducts.find(p => p.id === product.id);
    const isModified = this.lastSyncedProducts.find(p =>
      p.id === product.id && JSON.stringify(p) !== JSON.stringify(product)
    );

    const rowClass = isNew ? 'product-new' : (isModified ? 'product-modified' : '');
    const changeIndicator = isNew ? '🆕' : (isModified ? '📝' : '');

    // Safely escape HTML content to prevent XSS
    const escapedName = this.escapeHtml(product.name);
    const escapedDescription = this.escapeHtml(product.description.substring(0, 100));
    const escapedCategory = this.escapeHtml(product.category);
    const escapedId = this.escapeHtml(product.id);

    return `
      <tr class="product-row ${rowClass}">
        <td class="product-name-cell">
          <div class="product-name-container">
            <span class="change-indicator">${changeIndicator}</span>
            <strong>${escapedName}</strong>
            <div class="product-description-preview">${escapedDescription}...</div>
          </div>
        </td>
        <td><span class="category-badge">${escapedCategory}</span></td>
        <td>$${parseFloat(product.price).toFixed(2)}</td>
        <td class="images-count">${this.getImageCount(product)} images</td>
        <td>
          ${
            product.category === 'Books' && (product.averageRating || product.ratingsCount)
              ? `<span class="rating-badge">${typeof product.averageRating === 'number' ? product.averageRating.toFixed(2) : product.averageRating || '-'} / 5${product.ratingsCount ? ` (${product.ratingsCount})` : ''}</span>`
              : '<span class="rating-badge">-</span>'
          }
        </td>
        <td>
          <div class="status-indicators">
            <span class="status-badge good">Good</span>
            ${product.defects ? '<span class="status-badge defects">Defects</span>' : ''}
          </div>
        </td>
        <td>
          <div class="action-buttons">
            <button class="btn-edit" onclick="adminPanel.editProduct('${escapedId}')" title="Edit">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
              </svg>
            </button>
            <button class="btn-delete" onclick="adminPanel.deleteProduct('${escapedId}')" title="Delete">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
              </svg>
            </button>
          </div>
        </td>
      </tr>
    `;
  }

  renderPagination() {
    const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';
    
    // Previous button
    if (this.currentPage > 1) {
      paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage - 1})">‹</button>`;
    }
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
      if (i === this.currentPage) {
        paginationHTML += `<button class="page-btn active">${i}</button>`;
      } else {
        paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${i})">${i}</button>`;
      }
    }
    
    // Next button
    if (this.currentPage < totalPages) {
      paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage + 1})">›</button>`;
    }
    
    pagination.innerHTML = paginationHTML;

    // Update admin pagination info
    this.updateAdminPaginationInfo();
  }

  updateAdminPaginationInfo() {
    const paginationInfo = document.getElementById('admin-pagination-info');
    if (!paginationInfo) return;

    const totalProducts = this.filteredProducts.length;
    if (totalProducts === 0) {
      paginationInfo.textContent = 'No products found';
      return;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endIndex = Math.min(this.currentPage * this.itemsPerPage, totalProducts);

    paginationInfo.textContent = `Showing ${startIndex}-${endIndex} of ${totalProducts} products`;
  }

  goToPage(page) {
    this.currentPage = page;
    this.renderProducts();
  }

  initializeForm() {
    // Initialize the product form with all necessary fields
    const form = document.getElementById('product-form');
    if (!form) return;

    form.innerHTML = `
      <div class="compact-form-wrapper">
        <!-- Compact Form Grid Layout -->

        <div class="compact-form-grid">
          <!-- Row 1: Basic Info -->
          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>
            <div class="form-row">
              <div class="form-group">
                <label for="product-name">Product Name <span class="required">*</span></label>
                <input type="text" id="product-name" name="name" required placeholder="Enter product name" />
              </div>
              <div class="form-group">
                <label for="product-price">Price <span class="required">*</span></label>
                <div class="price-wrapper">
                  <span class="currency">$</span>
                  <input type="number" id="product-price" name="price" required placeholder="0.00" step="0.01" min="0" />
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="product-category">Category <span class="required">*</span></label>
                <select id="product-category" name="category" required onchange="adminPanel.handleCategoryChange(this.value)">
                  <option value="">Select category</option>
                  ${this.getCategories().map(category =>
                    `<option value="${category}">${category}</option>`
                  ).join('')}
                </select>
              </div>
              <div class="form-group">
                <label for="product-condition">Condition <span class="required">*</span></label>
                <select id="product-condition" name="condition" required>
                  <option value="">Select condition</option>
                  <option value="Poor">Poor</option>
                  <option value="Fair">Fair</option>
                  <option value="Good">Good</option>
                  <option value="Excellent">Excellent</option>
                  <option value="New">New</option>
                </select>
              </div>
            </div>

            <!-- Book-specific fields (hidden by default) -->
            <div id="book-fields" class="book-fields-section" style="display: none;">
              <h4 class="book-section-title">Book Information</h4>
              <div class="form-row">
                <div class="form-group">
                  <label for="book-olid">Open Library ID (OLID)</label>
                  <input type="text" id="book-olid" name="olid" placeholder="Enter OLID (e.g., OL123M)" />
                  <button type="button" id="lookup-olid" class="btn-lookup">Lookup by OLID</button>
                </div>
                <div class="form-group">
                  <label for="book-search">Search by Title</label>
                  <input type="text" id="book-search" name="bookSearch" placeholder="Enter book title" />
                  <button type="button" id="search-book" class="btn-lookup">Search Book</button>
                </div>
              </div>
              <div id="book-search-results" class="book-search-results" style="display: none;"></div>
              <div class="book-details" id="book-details" style="display: none;">
                <div class="form-row">
                  <div class="form-group">
                    <label for="book-authors">Authors</label>
                    <input type="text" id="book-authors" name="authors" placeholder="Authors (comma-separated)" />
                  </div>
                  <div class="form-group">
                    <label for="book-publisher">Publisher</label>
                    <input type="text" id="book-publisher" name="publisher" placeholder="Publisher" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label for="book-published-date">Published Date</label>
                    <input type="text" id="book-published-date" name="publishedDate" placeholder="Publication date" />
                  </div>
                  <div class="form-group">
                    <label for="book-page-count">Page Count</label>
                    <input type="number" id="book-page-count" name="pageCount" placeholder="Number of pages" />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label for="book-language">Language</label>
                    <input type="text" id="book-language" name="language" placeholder="Language" />
                  </div>
                  <div class="form-group">
                    <label for="book-rating">Average Rating</label>
                    <div style="display: flex; gap: 8px;">
                      <input type="text" id="book-rating" name="averageRating" placeholder="Rating (e.g., 4.5/5)" style="flex:1;" />
                      <input type="text" id="book-ratings-count" name="ratingsCount" placeholder="Ratings Count" style="width:90px;" readonly />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group full-width">
              <label for="product-description">Description <span class="required">*</span></label>
              <textarea id="product-description" name="description" required placeholder="Describe your product..." rows="3"></textarea>
            </div>
          </div>

          <!-- Row 2: Images & Features -->
          <div class="form-section">
            <h3 class="section-title">Images & Features</h3>
            <div class="form-row">
              <div class="form-group">
                <label for="product-images">Product Images</label>

                <!-- Image Upload Area -->
                <div class="image-upload-container">
                  <div class="upload-area" id="image-upload-area">
                    <div class="upload-content">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                      </svg>
                      <h4>Upload Images</h4>
                      <p>Drag and drop images here, or click to select</p>
                      <button type="button" class="btn-upload">Choose Files</button>
                    </div>
                    <input type="file" id="image-file-input" multiple accept="image/*" style="display: none;">
                  </div>

                  <!-- Upload Progress -->
                  <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                      <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <span class="progress-text" id="progress-text">Uploading...</span>
                  </div>

                  <!-- Image Previews -->
                  <div class="image-previews" id="image-previews"></div>
                </div>

                <!-- Hidden field to store uploaded image URLs -->
                <textarea id="product-images" name="images" style="display: none;"></textarea>
              </div>
              <div class="form-group">
                <label>Key Features</label>
                <div class="features-area" id="features-display">
                  <!-- Features will be displayed here -->
                </div>
                <div class="feature-input">
                  <input type="text" id="feature-label" placeholder="Feature (e.g., Material)" />
                  <input type="text" id="feature-value" placeholder="Value (e.g., Cotton)" />
                  <button type="button" id="add-feature" class="btn-add-feature">+</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Row 3: Additional Notes -->
          <div class="form-section">
            <h3 class="section-title">Additional Notes</h3>
            <div class="form-group full-width">
              <label for="product-defects">Defects/Condition Notes</label>
              <textarea id="product-defects" name="defects" placeholder="Describe any defects, wear, or condition issues..." rows="3"></textarea>
              <small class="help-text warning">Be honest about any defects to maintain customer trust.</small>
            </div>
          </div>
        </div>
      </div>
    `;

    // Initialize book fields visibility - check after a short delay to ensure DOM is ready
    setTimeout(() => {
      const categorySelect = document.getElementById('product-category');
      if (categorySelect && categorySelect.value) {
        this.handleCategoryChange(categorySelect.value);
      }
    }, 100);
  }

  bindFormEvents() {
    // Add feature functionality
    const addFeatureBtn = document.getElementById('add-feature');
    if (addFeatureBtn) {
      addFeatureBtn.addEventListener('click', () => {
        this.addKeyPoint();
      }, { passive: true });
    }

    // Enter key support for adding features
    const featureLabel = document.getElementById('feature-label');
    const featureValue = document.getElementById('feature-value');

    if (featureLabel && featureValue) {
      [featureLabel, featureValue].forEach(input => {
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            this.addKeyPoint();
          }
        }, { passive: false }); // Must be non-passive for preventDefault
      });
    }

    // Category change handler is now handled via onchange attribute in HTML

    // Book lookup handlers
    const lookupOlidBtn = document.getElementById('lookup-olid');
    if (lookupOlidBtn) {
      lookupOlidBtn.addEventListener('click', () => {
        this.lookupBookByOLID();
      });
    }
    // Enter key support for OLID lookup
    const olidInput = document.getElementById('book-olid');
    if (olidInput) {
      olidInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          this.lookupBookByOLID();
        }
      });
    }
    // Book name search handler
    const searchBookBtn = document.getElementById('search-book');
    if (searchBookBtn) {
      searchBookBtn.addEventListener('click', () => {
        this.searchBookByTitle();
      });
    }
    // Enter key support for book name search
    const bookSearchInput = document.getElementById('book-search');
    if (bookSearchInput) {
      bookSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          this.searchBookByTitle();
        }
      });
    }
    // Bind image upload events
    this.bindImageUploadEvents();
  }

  bindImageUploadEvents() {
    const uploadArea = document.getElementById('image-upload-area');
    const fileInput = document.getElementById('image-file-input');
    const uploadButton = uploadArea?.querySelector('.btn-upload');

    if (!uploadArea || !fileInput || !uploadButton) return;

    // Click to select files
    uploadButton.addEventListener('click', () => {
      fileInput.click();
    });

    uploadArea.addEventListener('click', (e) => {
      if (e.target === uploadArea || e.target.closest('.upload-content')) {
        fileInput.click();
      }
    });

    // File input change
    fileInput.addEventListener('change', (e) => {
      const files = Array.from(e.target.files || []);
      if (files.length > 0) {
        this.handleImageUpload(files);
      }
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('drag-over');
    });

    uploadArea.addEventListener('dragleave', (e) => {
      e.preventDefault();
      if (!uploadArea.contains(e.relatedTarget)) {
        uploadArea.classList.remove('drag-over');
      }
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('drag-over');

      const files = Array.from(e.dataTransfer?.files || []).filter(file =>
        file.type.startsWith('image/')
      );

      if (files.length > 0) {
        this.handleImageUpload(files);
      }
    });
  }

  async handleImageUpload(files) {
    const progressContainer = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const previewsContainer = document.getElementById('image-previews');

    if (!progressContainer || !progressFill || !progressText || !previewsContainer) return;

    try {
      // Show progress
      progressContainer.style.display = 'block';
      progressText.textContent = 'Processing images...';
      progressFill.style.width = '0%';

      // Import image resizer
      const { imageResizer } = await import('../utils/imageResizer.js');

      // Validate files first
      const validFiles = [];
      for (const file of files) {
        const validation = imageResizer.validateFile(file);
        if (validation.valid) {
          validFiles.push(file);
        } else {
          console.warn(`Skipping invalid file ${file.name}: ${validation.error}`);
        }
      }

      if (validFiles.length === 0) {
        throw new Error('No valid image files selected');
      }

      // Process images to create variants
      progressText.textContent = 'Creating image variants...';
      progressFill.style.width = '20%';

      console.log('🔄 Starting image processing for files:', validFiles.map(f => f.name));

      const processedImages = await imageResizer.processFiles(validFiles, (progress) => {
        const percentage = 20 + (progress.current / progress.total) * 30; // 20-50%
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = `Processing ${progress.filename} (${progress.current}/${progress.total})`;
      });

      console.log('✅ Image processing complete. Results:', processedImages.map(p => ({
        success: p.success,
        baseFilename: p.baseFilename,
        variantCount: p.variants ? Object.keys(p.variants).length : 0
      })));

      // Create form data with all variants
      const formData = new FormData();
      let totalVariants = 0;

      for (const processedImage of processedImages) {
        if (!processedImage.success) {
          console.error(`Failed to process ${processedImage.originalFile.name}:`, processedImage.error);
          continue;
        }

        // Add each variant to form data
        for (const [variantName, variant] of Object.entries(processedImage.variants)) {
          const file = new File([variant.blob], `${variant.filename}|${variantName}`, {
            type: 'image/webp'
          });
          formData.append('images', file);
          totalVariants++;
        }
      }

      // Upload all variants
      progressText.textContent = `Uploading ${totalVariants} image variants...`;
      progressFill.style.width = '60%';

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        body: formData
      });

      // Check if response is ok and has content
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // Check if response has content
      const responseText = await response.text();
      if (!responseText) {
        throw new Error('Empty response from server');
      }

      // Try to parse JSON
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Response text:', responseText);
        throw new Error(`Invalid JSON response: ${parseError.message}`);
      }

      if (result.success && result.uploaded.length > 0) {
        // Update progress
        progressFill.style.width = '90%';
        progressText.textContent = 'Organizing image variants...';

        // Group uploaded variants by base image
        const imageGroups = this.groupImageVariants(result.uploaded, processedImages);

        // Update progress
        progressFill.style.width = '100%';
        progressText.textContent = `Successfully uploaded ${imageGroups.length} image(s) with variants`;

        // Store image data in structured format
        const textarea = document.getElementById('product-images');
        const currentData = textarea.value.trim();
        console.log('📝 Current textarea data before upload:', currentData);

        let imageData = [];

        if (currentData) {
          try {
            imageData = JSON.parse(currentData);
            console.log('📊 Parsed existing image data:', imageData);
          } catch (e) {
            // If current data is old format (newline-separated URLs), convert it
            imageData = currentData.split('\n').filter(url => url.trim()).map(url => ({
              original: url,
              variants: { original: url }
            }));
            console.log('🔄 Converted old format to new:', imageData);
          }
        }

        console.log('➕ Adding new image groups:', imageGroups);
        console.log('📊 Image data before adding new groups:', imageData);

        // Add new image groups
        imageData.push(...imageGroups);

        console.log('📊 Image data after adding new groups:', imageData);

        // Deduplicate the combined image data to prevent duplicates
        imageData = this.deduplicateImages(imageData);
        console.log('🔄 Image data after deduplication:', imageData);

        // Store as JSON
        textarea.value = JSON.stringify(imageData, null, 2);
        console.log('💾 Final textarea value:', textarea.value);

        // Show ALL image previews (existing + new) to prevent duplicates
        const allPreviewData = imageData.map(group => ({
          url: group.variants.original || group.variants.desktop || Object.values(group.variants)[0],
          filename: group.baseFilename || 'unknown',
          variants: group.variants
        }));

        this.showImagePreviews(allPreviewData);

        // Show success message
        if (window.showToast) {
          window.showToast(
            'Upload Successful',
            `${imageGroups.length} image(s) uploaded with ${Object.keys(imageGroups[0]?.variants || {}).length} variants each`,
            'success',
            4000
          );
        }

        // Hide progress after delay
        setTimeout(() => {
          progressContainer.style.display = 'none';
        }, 2000);

      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error) {
      console.error('Upload error:', error);

      // Hide progress and show error
      progressContainer.style.display = 'none';

      if (window.showToast) {
        window.showToast(
          'Upload Failed',
          error.message || 'Failed to upload images',
          'error',
          5000
        );
      }
    }
  }

  groupImageVariants(uploadedFiles, processedImages) {
    console.log('🔄 Grouping image variants...');
    console.log('📤 Uploaded files:', uploadedFiles);
    console.log('🖼️ Processed images:', processedImages);

    const groups = [];

    // Create a map of processed images by base filename
    const processedMap = new Map();
    processedImages.forEach(processed => {
      if (processed.success) {
        processedMap.set(processed.baseFilename, processed);
        console.log('✅ Processed image mapped:', processed.baseFilename);
      }
    });

    // Group uploaded files by base filename
    const uploadGroups = new Map();
    uploadedFiles.forEach(uploaded => {
      const filename = uploaded.filename || uploaded.originalName;
      const baseFilename = filename.split('|')[0].replace(/-(thumb|mobile|tablet|desktop|original)\.webp$/, '');

      console.log('📁 Processing uploaded file:', filename, '→ base:', baseFilename);

      if (!uploadGroups.has(baseFilename)) {
        uploadGroups.set(baseFilename, {
          baseFilename: baseFilename,
          variants: {}
        });
        console.log('🆕 Created new group for:', baseFilename);
      }

      // Determine variant type from filename
      const variantMatch = filename.match(/-(thumb|mobile|tablet|desktop|original)\.webp/);
      const variantType = variantMatch ? variantMatch[1] : 'original';

      uploadGroups.get(baseFilename).variants[variantType] = uploaded.url;
      console.log('🔗 Added variant:', variantType, 'to', baseFilename);
    });

    const result = Array.from(uploadGroups.values());
    console.log('📊 Final grouped result:', result);
    return result;
  }

  showImagePreviews(uploadedImages) {
    const previewsContainer = document.getElementById('image-previews');
    if (!previewsContainer) return;

    // Clear existing previews to prevent duplicates
    previewsContainer.innerHTML = '';

    uploadedImages.forEach(item => {
      try {
        const preview = document.createElement('div');
        preview.className = 'image-preview';

        const variantCount = item.variants ? Object.keys(item.variants).length : 1;
        const displayUrl = item.url || (item.variants && Object.values(item.variants)[0]);

        // Validate display URL
        if (!displayUrl || typeof displayUrl !== 'string') {
          console.warn('Invalid display URL for image:', item);
          return;
        }

        // Create image element with error handling
        const img = document.createElement('img');
        img.src = displayUrl;
        img.alt = 'Uploaded image';
        img.loading = 'lazy';
        img.onerror = () => {
          img.src = '/images/product-placeholder.svg';
          img.alt = 'Image failed to load';
        };

        // Create preview info
        const previewInfo = document.createElement('div');
        previewInfo.className = 'preview-info';

        const filenameSpan = document.createElement('span');
        filenameSpan.className = 'preview-filename';
        filenameSpan.textContent = this.sanitizeInput(item.filename || 'Unknown');

        const variantsSpan = document.createElement('span');
        variantsSpan.className = 'preview-variants';
        variantsSpan.textContent = `${variantCount} variants`;

        // Create action buttons
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'preview-actions';

        const featuredBtn = document.createElement('button');
        featuredBtn.type = 'button';
        featuredBtn.className = 'btn-set-featured';
        featuredBtn.title = 'Set as featured image';
        featuredBtn.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
          </svg>
        `;

        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn-remove-image';
        removeBtn.title = 'Remove image';
        removeBtn.textContent = '×';

        // Add event listeners
        featuredBtn.addEventListener('click', () => {
          this.setFeaturedImage(item.url || displayUrl);
        });

        removeBtn.addEventListener('click', () => {
          this.removeImageFromTextarea(item.url || displayUrl);
          preview.remove();
          // If this was the featured image, clear it
          if (this.featuredImageUrl === (item.url || displayUrl)) {
            this.setFeaturedImage(null);
          }
        });

        // Assemble the preview
        actionsDiv.appendChild(featuredBtn);
        actionsDiv.appendChild(removeBtn);
        previewInfo.appendChild(filenameSpan);
        previewInfo.appendChild(variantsSpan);
        previewInfo.appendChild(actionsDiv);
        preview.appendChild(img);
        preview.appendChild(previewInfo);
        previewsContainer.appendChild(preview);

      } catch (error) {
        console.error('Error creating image preview:', error, item);
      }
    });
  }

  setFeaturedImage(imageUrl) {
    this.featuredImageUrl = imageUrl;

    // Update visual indicators
    const previews = document.querySelectorAll('.image-preview');
    previews.forEach(preview => {
      const img = preview.querySelector('img');
      const featuredBtn = preview.querySelector('.btn-set-featured');

      if (img && featuredBtn) {
        const isCurrentFeatured = img.src === imageUrl;

        // Update button state
        featuredBtn.classList.toggle('active', isCurrentFeatured);
        featuredBtn.title = isCurrentFeatured ? 'Featured image' : 'Set as featured image';

        // Update preview styling
        preview.classList.toggle('featured', isCurrentFeatured);
      }
    });

    // Show feedback
    if (window.showToast) {
      if (imageUrl) {
        window.showToast(
          'Featured Image Set',
          'This image will be displayed first in product listings',
          'success',
          2000
        );
      } else {
        window.showToast(
          'Featured Image Cleared',
          'The first image will be used as the featured image',
          'info',
          2000
        );
      }
    }
  }

  removeImageFromTextarea(urlToRemove) {
    const textarea = document.getElementById('product-images');
    const currentData = textarea.value.trim();

    console.log('🗑️ Removing image:', urlToRemove);
    console.log('📝 Current textarea data:', currentData);

    if (!currentData) return;

    try {
      // Try to parse as JSON (new format)
      const imageData = JSON.parse(currentData);
      console.log('📊 Parsed image data:', imageData);

      // Filter out the image with the matching URL
      const filteredData = imageData.filter(imageObj => {
        // Check if any variant URL matches the URL to remove
        const variantUrls = Object.values(imageObj.variants || {});
        const shouldKeep = !variantUrls.includes(urlToRemove);
        console.log('🔍 Checking image:', imageObj.baseFilename, 'URLs:', variantUrls, 'Keep:', shouldKeep);
        return shouldKeep;
      });

      console.log('✅ Filtered data:', filteredData);

      // Update textarea with filtered data
      textarea.value = JSON.stringify(filteredData, null, 2);
      console.log('💾 Updated textarea value');

      // Refresh previews with remaining images
      const remainingPreviewData = filteredData.map(group => ({
        url: group.variants.original || group.variants.desktop || Object.values(group.variants)[0],
        filename: group.baseFilename || 'unknown',
        variants: group.variants
      }));

      this.showImagePreviews(remainingPreviewData);

    } catch (e) {
      console.log('⚠️ JSON parse failed, using old format');
      // Fall back to old format (newline-separated URLs)
      const urls = currentData.split('\n').filter(url => url.trim() !== urlToRemove.trim());
      textarea.value = urls.join('\n');

      // Refresh previews for old format
      const remainingPreviewData = urls.map((url, index) => ({
        url: url,
        filename: `existing-image-${index + 1}.jpg`
      }));

      this.showImagePreviews(remainingPreviewData);
    }
  }

  // Custom styled confirmation dialog
  showConfirmDialog(title, message, details, confirmText = 'Confirm', type = 'danger') {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.className = 'confirm-modal-overlay';
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(4px);
      `;

      // Create modal
      const modal = document.createElement('div');
      modal.className = 'confirm-modal';

      const typeColors = {
        danger: '#dc2626',
        warning: '#d97706',
        info: '#92400e',
        success: '#059669'
      };

      modal.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        transition: transform 0.2s ease;
      `;

      modal.innerHTML = `
        <div class="confirm-header" style="margin-bottom: 1rem;">
          <h3 style="margin: 0; color: #0f172a; font-size: 1.25rem; font-weight: 600;">${title}</h3>
        </div>
        <div class="confirm-body" style="margin-bottom: 2rem;">
          <p style="margin: 0 0 0.5rem 0; color: #374151; font-size: 1rem;">${message}</p>
          ${details ? `<p style="margin: 0; color: #6b7280; font-size: 0.875rem;">${details}</p>` : ''}
        </div>
        <div class="confirm-actions" style="display: flex; gap: 0.75rem; justify-content: flex-end;">
          <button class="confirm-cancel" style="
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
          ">Cancel</button>
          <button class="confirm-action" style="
            padding: 0.5rem 1rem;
            border: none;
            background: ${typeColors[type] || typeColors.danger};
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
          ">${confirmText}</button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Animate in
      requestAnimationFrame(() => {
        modal.style.transform = 'scale(1)';
      });

      // Event handlers
      const cleanup = () => {
        overlay.style.opacity = '0';
        modal.style.transform = 'scale(0.95)';
        setTimeout(() => {
          if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
          }
        }, 200);
      };

      modal.querySelector('.confirm-cancel').addEventListener('click', () => {
        cleanup();
        resolve(false);
      });

      modal.querySelector('.confirm-action').addEventListener('click', () => {
        cleanup();
        resolve(true);
      });

      // ESC key support
      const handleKeydown = (e) => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(false);
          document.removeEventListener('keydown', handleKeydown);
        }
      };
      document.addEventListener('keydown', handleKeydown);

      // Click outside to close
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          cleanup();
          resolve(false);
        }
      });
    });
  }

  addKeyPoint() {
    const labelInput = document.getElementById('feature-label');
    const valueInput = document.getElementById('feature-value');

    if (!labelInput || !valueInput) return;

    const label = this.sanitizeInput(labelInput.value.trim());
    const value = this.sanitizeInput(valueInput.value.trim());

    // Validate input length and content
    if (label && value && label.length <= 100 && value.length <= 500) {
      this.keyPoints.push({ label, value });
      this.renderKeyPoints();
      labelInput.value = '';
      valueInput.value = '';
      labelInput.focus();
    } else if (label.length > 100 || value.length > 500) {
      if (window.showToast) {
        window.showToast(
          'Input Too Long',
          'Label must be 100 characters or less, value must be 500 characters or less.',
          'error',
          4000
        );
      }
    }
  }

  // Sanitize user input to prevent XSS
  sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    // Remove potentially dangerous characters and HTML tags
    return input
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[<>'"&]/g, '') // Remove dangerous characters
      .trim();
  }

  renderKeyPoints() {
    const container = document.getElementById('features-display');
    if (!container) return;

    // Clear container first
    container.innerHTML = '';

    // Create elements safely to prevent XSS
    this.keyPoints.forEach((point, index) => {
      const keypointItem = document.createElement('div');
      keypointItem.className = 'keypoint-item';

      const labelSpan = document.createElement('span');
      labelSpan.className = 'keypoint-label';
      labelSpan.textContent = `${point.label}:`;

      const valueSpan = document.createElement('span');
      valueSpan.className = 'keypoint-value';
      valueSpan.textContent = point.value;

      const removeBtn = document.createElement('button');
      removeBtn.type = 'button';
      removeBtn.className = 'btn-remove-keypoint';
      removeBtn.title = 'Remove';
      removeBtn.textContent = '×';
      removeBtn.addEventListener('click', () => this.removeKeyPoint(index));

      keypointItem.appendChild(labelSpan);
      keypointItem.appendChild(valueSpan);
      keypointItem.appendChild(removeBtn);
      container.appendChild(keypointItem);
    });
  }

  removeKeyPoint(index) {
    this.keyPoints.splice(index, 1);
    this.renderKeyPoints();
  }

  editProduct(productId) {
    const product = this.products.find(p => p.id === productId);
    if (!product) return;

    this.editingProduct = product;
    this.switchTab('form');
    this.populateForm(product);
    document.getElementById('form-title').textContent = 'Edit Product';
  }

  populateForm(product) {
    console.log('📝 Populating form with product:', product.name);
    console.log('🖼️ Product images data:', product.images);

    // Populate basic fields
    document.getElementById('product-name').value = product.name || '';
    document.getElementById('product-description').value = product.description || '';
    document.getElementById('product-category').value = product.category || '';
    document.getElementById('product-condition').value = product.condition || '';
    document.getElementById('product-price').value = product.price || '';
    document.getElementById('product-defects').value = product.defects || '';

    // Populate images - handle both old and new formats
    if (product.images && product.images.length > 0) {
      // Deduplicate images before populating
      const deduplicatedImages = this.deduplicateImages(product.images);
      console.log('🔄 Deduplicated images for form:', deduplicatedImages);

      // Check if this is the new format (array of objects with variants)
      if (typeof deduplicatedImages[0] === 'object' && deduplicatedImages[0].variants) {
        // New format: display as JSON
        document.getElementById('product-images').value = JSON.stringify(deduplicatedImages, null, 2);

        // Set featured image
        this.featuredImageUrl = product.featuredImage || null;

        // Show image previews for new format
        const existingImages = deduplicatedImages.map((imageObj, index) => ({
          url: imageObj.variants.original || imageObj.variants.desktop || Object.values(imageObj.variants)[0],
          filename: imageObj.baseFilename || `existing-image-${index + 1}`,
          variants: imageObj.variants
        }));
        this.showImagePreviews(existingImages);
      } else {
        // Old format: array of URL strings
        document.getElementById('product-images').value = deduplicatedImages.join('\n');

        // Set featured image
        this.featuredImageUrl = product.featuredImage || null;

        // Show image previews for old format
        const existingImages = deduplicatedImages.map((url, index) => ({
          url: url,
          filename: `existing-image-${index + 1}.jpg`
        }));
        this.showImagePreviews(existingImages);
      }

      // Update featured image indicators after previews are shown
      if (this.featuredImageUrl) {
        setTimeout(() => this.setFeaturedImage(this.featuredImageUrl), 100);
      }
    }

    // Populate key points
    this.keyPoints = [...(product.keyPoints || [])];
    this.renderKeyPoints();

    // Handle category change to show/hide book fields
    this.handleCategoryChange(product.category);

    // Populate book fields if it's a book
    if (product.category === 'Books') {
      const bookFields = {
        'book-authors': product.authors ? product.authors.join(', ') : '',
        'book-publisher': product.publisher || '',
        'book-published-date': product.publishedDate || '',
        'book-page-count': product.pageCount || '',
        'book-language': product.language || '',
        'book-rating': product.averageRating ? `${product.averageRating}/5` : '',
        'book-ratings-count': product.ratingsCount ? `${product.ratingsCount}` : ''
      };
      Object.entries(bookFields).forEach(([fieldId, value]) => {
        const field = document.getElementById(fieldId);
        if (field) {
          field.value = value;
        }
      });
      // Show book details section if any book data exists
      if (Object.values(bookFields).some(value => value)) {
        const bookDetails = document.getElementById('book-details');
        if (bookDetails) {
          bookDetails.style.display = 'block';
        }
      }
    }
  }

  resetForm() {
    this.editingProduct = null;
    this.keyPoints = [];
    this.featuredImageUrl = null; // Clear featured image
    document.getElementById('form-title').textContent = 'Add New Product';

    // Reset all form fields
    const form = document.getElementById('product-form');
    if (form) {
      const inputs = form.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.value = '';
      });
    }

    // Clear book fields and hide book section
    this.clearBookFields();

    // Clear image previews
    const previewsContainer = document.getElementById('image-previews');
    if (previewsContainer) {
      previewsContainer.innerHTML = '';
    }

    this.renderKeyPoints();
  }

  saveProduct() {
    const form = document.getElementById('product-form');
    if (!form) return;

    // Get form data
    const formData = new FormData(form);
    const rawImageData = formData.get('images');
    console.log('💾 Save Product - Raw image data from form:', rawImageData);

    const parsedImages = this.parseImageData(rawImageData);
    console.log('📊 Save Product - Parsed images:', parsedImages);

    const productData = {
      name: formData.get('name')?.trim(),
      description: formData.get('description')?.trim(),
      category: formData.get('category'),
      condition: formData.get('condition'),
      price: parseFloat(formData.get('price')) || 0,
      defects: formData.get('defects')?.trim() || '',
      images: parsedImages,
      featuredImage: this.featuredImageUrl || null,
      keyPoints: [...this.keyPoints],
      dateAdded: new Date().toISOString()
    };

    // Add book-specific fields if category is Books
    if (productData.category === 'Books') {
      const authors = document.getElementById('book-authors')?.value?.trim();
      const publisher = document.getElementById('book-publisher')?.value?.trim();
      const publishedDate = document.getElementById('book-published-date')?.value?.trim();
      const pageCount = document.getElementById('book-page-count')?.value?.trim();
      const language = document.getElementById('book-language')?.value?.trim();
      const rating = document.getElementById('book-rating')?.value?.trim();
      const ratingsCount = document.getElementById('book-ratings-count')?.value?.trim();

      if (authors) productData.authors = authors.split(',').map(a => a.trim()).filter(a => a);
      if (publisher) productData.publisher = publisher;
      if (publishedDate) productData.publishedDate = publishedDate;
      if (pageCount) productData.pageCount = parseInt(pageCount) || undefined;
      if (language) productData.language = language;
      // Parse averageRating if it exists (format: "4.5/5" or just "4.5")
      if (rating) {
        const ratingMatch = rating.match(/^([\d.]+)/);
        if (ratingMatch) {
          productData.averageRating = parseFloat(ratingMatch[1]);
        }
      }
      if (ratingsCount && !isNaN(ratingsCount)) {
        productData.ratingsCount = parseInt(ratingsCount);
      }
    }

    // Validate required fields
    if (!productData.name || !productData.description || !productData.category || !productData.condition || !productData.price) {
      if (window.showToast) {
        window.showToast(
          'Validation Error',
          'Please fill in all required fields: Name, Description, Category, Condition, and Price.',
          'error',
          4000
        );
      }
      return;
    }

    if (this.editingProduct) {
      // Update existing product
      productData.id = this.editingProduct.id;
      const index = this.products.findIndex(p => p.id === this.editingProduct.id);
      if (index !== -1) {
        this.products[index] = productData;
      }
    } else {
      // Add new product
      productData.id = 'product-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      this.products.push(productData);
    }

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.switchTab('list');
    this.resetForm();
    this.filterAndRenderProducts();

    // Show success message
    if (window.showToast) {
      window.showToast(
        this.editingProduct ? 'Product Updated' : 'Product Added',
        this.editingProduct ? 'Product updated successfully!' : 'Product added successfully!',
        'success',
        3000
      );
    }
  }

  async deleteProduct(productId) {
    // Create a custom confirmation dialog instead of using alert
    const product = this.products.find(p => p.id === productId);
    const productName = product ? product.name : 'this product';

    // Use custom styled confirmation dialog
    const confirmed = await this.showConfirmDialog(
      'Delete Product',
      `Are you sure you want to delete "${productName}"?`,
      'This action cannot be undone and will remove the product from your catalog.',
      'Delete Product',
      'danger'
    );

    if (!confirmed) return;

    const index = this.products.findIndex(p => p.id === productId);
    if (index !== -1) {
      this.products.splice(index, 1);
      this.hasUnsavedChanges = true;
      this.updateSyncButtonState();
      this.filterAndRenderProducts();

      // Show success toast
      if (window.showToast) {
        window.showToast(
          'Product Deleted',
          `"${productName}" has been deleted successfully.`,
          'warning',
          3000
        );
      }
    }
  }

  renderCategories() {
    const container = document.getElementById('categories-grid');
    if (!container) return;

    const categories = this.getCategories();

    if (categories.length === 0) {
      container.innerHTML = '<div class="no-categories">No categories found. Add your first category to get started.</div>';
      return;
    }

    // Create compact list layout
    container.innerHTML = `
      <div class="categories-list">
        <div class="categories-list-header">
          <div class="category-name-col">Category Name</div>
          <div class="category-count-col">Products</div>
          <div class="category-status-col">Status</div>
          <div class="category-actions-col">Actions</div>
        </div>
        <div class="categories-list-body">
          ${categories.map(category => {
            const productCount = this.products.filter(p => p.category === category).length;
            const isCreatedCategory = this.createdCategories.has(category) && productCount === 0;

            return `
              <div class="category-list-item ${isCreatedCategory ? 'category-unused' : ''}">
                <div class="category-name-col">
                  <div class="category-name">${category}</div>
                </div>
                <div class="category-count-col">
                  <span class="product-count">${productCount}</span>
                </div>
                <div class="category-status-col">
                  <span class="status-badge ${isCreatedCategory ? 'status-ready' : 'status-active'}">
                    ${isCreatedCategory ? 'Ready' : 'Active'}
                  </span>
                </div>
                <div class="category-actions-col">
                  <div class="category-actions">
                    <button class="btn-edit-category" onclick="adminPanel.editCategory('${category}')" title="Edit Category">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                      </svg>
                    </button>
                    <button class="btn-delete-category" onclick="adminPanel.deleteCategory('${category}')" title="Delete Category">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    `;
  }

  showCategoryForm() {
    const container = document.getElementById('category-form-container');
    const categoriesContainer = document.querySelector('.categories-container');
    if (container) {
      container.style.display = 'block';
      // Add class for browsers that don't support :has()
      if (categoriesContainer) {
        categoriesContainer.classList.add('form-visible');
      }
      document.getElementById('category-name').focus();
    }
  }

  hideCategoryForm() {
    const container = document.getElementById('category-form-container');
    const categoriesContainer = document.querySelector('.categories-container');
    if (container) {
      container.style.display = 'none';
      // Remove class for browsers that don't support :has()
      if (categoriesContainer) {
        categoriesContainer.classList.remove('form-visible');
      }
      document.getElementById('category-form').reset();
      this.editingCategory = null;
    }
  }

  saveCategory() {
    const name = document.getElementById('category-name').value.trim();
    const description = document.getElementById('category-description').value.trim();

    if (!name) {
      if (window.showToast) {
        window.showToast(
          'Validation Error',
          'Please enter a category name.',
          'error',
          4000
        );
      }
      return;
    }

    // Check if category already exists (case-insensitive)
    const existingCategories = this.getCategories();
    const categoryExists = existingCategories.some(cat =>
      cat.toLowerCase() === name.toLowerCase()
    );

    if (categoryExists && !this.editingCategory) {
      if (window.showToast) {
        window.showToast(
          'Category Exists',
          'A category with this name already exists.',
          'error',
          4000
        );
      }
      return;
    }

    // If editing, update products that use the old category name
    if (this.editingCategory && this.editingCategory !== name) {
      this.products.forEach(product => {
        if (product.category === this.editingCategory) {
          product.category = name;
        }
      });

      // Update created categories set
      this.createdCategories.delete(this.editingCategory);
      this.createdCategories.add(name);
    } else if (!this.editingCategory) {
      // Add new category to created categories set
      this.createdCategories.add(name);
    }

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.hideCategoryForm();
    this.renderCategories();
    this.renderStats();

    if (window.showToast) {
      window.showToast(
        this.editingCategory ? 'Category Updated' : 'Category Ready',
        this.editingCategory
          ? `Category "${name}" has been updated successfully. Category pages will be generated on next sync.`
          : `Category "${name}" is ready to use. Create products with this category to see it in the list. Category pages will be generated automatically.`,
        'success',
        4000
      );
    }
  }

  editCategory(categoryName) {
    this.editingCategory = categoryName;
    document.getElementById('category-name').value = categoryName;
    document.getElementById('category-form-title').textContent = 'Edit Category';
    this.showCategoryForm();
  }

  async deleteCategory(categoryName) {
    // Check how many products use this category
    const productsUsingCategory = this.products.filter(p => p.category === categoryName);

    // Use custom styled confirmation dialog
    const confirmed = await this.showConfirmDialog(
      'Delete Category',
      `Are you sure you want to delete the "${categoryName}" category?`,
      productsUsingCategory.length > 0
        ? `This will affect ${productsUsingCategory.length} product(s). Those products will need to be assigned to a different category.`
        : 'This action cannot be undone.',
      'Delete Category',
      'danger'
    );

    if (!confirmed) return;

    if (productsUsingCategory.length > 0) {
      // Show a dialog to choose what to do with products
      const action = await this.showCategoryDeletionDialog(categoryName, productsUsingCategory.length);

      if (action === 'cancel') return;

      if (action === 'uncategorized') {
        // Move products to "Uncategorized"
        productsUsingCategory.forEach(product => {
          product.category = 'Uncategorized';
        });
      } else if (action.startsWith('move-to-')) {
        // Move products to another category
        const targetCategory = action.replace('move-to-', '');
        productsUsingCategory.forEach(product => {
          product.category = targetCategory;
        });
      }
    }

    // Remove from created categories set
    this.createdCategories.delete(categoryName);

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.renderCategories();
    this.renderStats();
    this.filterAndRenderProducts();

    if (window.showToast) {
      window.showToast(
        'Category Deleted',
        `Category "${categoryName}" has been deleted successfully.`,
        'success',
        3000
      );
    }
  }

  // Category deletion dialog with options
  showCategoryDeletionDialog(categoryName, productCount) {
    return new Promise((resolve) => {
      const availableCategories = this.getCategories().filter(cat => cat !== categoryName);

      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.className = 'confirm-modal-overlay';
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `;

      // Create modal
      const modal = document.createElement('div');
      modal.className = 'confirm-modal';
      modal.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `;

      const categoryOptions = availableCategories.length > 0
        ? availableCategories.map(cat =>
            `<label style="display: block; margin: 0.5rem 0; cursor: pointer;">
              <input type="radio" name="category-action" value="move-to-${cat}" style="margin-right: 0.5rem;">
              Move to "${cat}"
            </label>`
          ).join('')
        : '';

      modal.innerHTML = `
        <h3 style="margin: 0 0 1rem 0; color: #dc2626;">What should happen to the ${productCount} product(s)?</h3>
        <div style="margin: 1rem 0;">
          <label style="display: block; margin: 0.5rem 0; cursor: pointer;">
            <input type="radio" name="category-action" value="uncategorized" style="margin-right: 0.5rem;" checked>
            Move to "Uncategorized"
          </label>
          ${categoryOptions}
        </div>
        <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
          <button id="cancel-deletion" style="padding: 0.5rem 1rem; border: 1px solid #d1d5db; background: white; border-radius: 4px; cursor: pointer;">Cancel</button>
          <button id="confirm-deletion" style="padding: 0.5rem 1rem; background: #dc2626; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete Category</button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Handle buttons
      const cancelBtn = modal.querySelector('#cancel-deletion');
      const confirmBtn = modal.querySelector('#confirm-deletion');

      cancelBtn.addEventListener('click', () => {
        document.body.removeChild(overlay);
        resolve('cancel');
      });

      confirmBtn.addEventListener('click', () => {
        const selectedAction = modal.querySelector('input[name="category-action"]:checked')?.value || 'uncategorized';
        document.body.removeChild(overlay);
        resolve(selectedAction);
      });

      // Close on overlay click
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          document.body.removeChild(overlay);
          resolve('cancel');
        }
      });
    });
  }

  updateSyncButtonState() {
    const syncBtn = document.getElementById('sync-products');
    if (!syncBtn) return;

    if (this.hasUnsavedChanges) {
      syncBtn.classList.add('has-changes');
      syncBtn.title = 'You have unsaved changes. Click to sync and deploy.';
    } else {
      syncBtn.classList.remove('has-changes');
      syncBtn.title = 'Sync with server and deploy';
    }
  }

  async checkGitHubStatus() {
    const indicator = document.getElementById('github-indicator');
    const text = document.getElementById('github-text');

    if (!indicator || !text) return;

    // Simulate GitHub status check
    indicator.textContent = '🟡';
    text.textContent = 'Checking...';

    setTimeout(() => {
      indicator.textContent = '🟢';
      text.textContent = 'Connected';
    }, 1000);
  }

  async testGitHub() {
    const indicator = document.getElementById('github-indicator');
    const text = document.getElementById('github-text');

    if (!indicator || !text) return;

    indicator.textContent = '🟡';
    text.textContent = 'Testing...';

    try {
      // Call the real GitHub test API
      const response = await fetch('/api/github-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'test-commit'
        })
      });

      const result = await response.json();
      console.log('GitHub test result:', result);

      if (result.success) {
        indicator.textContent = '🟢';
        text.textContent = 'Test successful';
        if (window.showToast) {
          window.showToast(
            'GitHub Test Successful',
            `Repository: ${result.repoInfo?.full_name || 'Connected'}\nCommit SHA: ${result.commitSha || 'N/A'}`,
            'success',
            5000
          );
        }
      } else {
        indicator.textContent = '🔴';
        text.textContent = 'Test failed';
        if (window.showToast) {
          window.showToast(
            'GitHub Test Failed',
            `${result.error || 'Unknown error'}\n\nPlease check your GitHub configuration.`,
            'error',
            6000
          );
        }
      }
    } catch (error) {
      console.error('GitHub test error:', error);
      indicator.textContent = '🔴';
      text.textContent = 'Test failed';
      if (window.showToast) {
        window.showToast(
          'GitHub Test Error',
          `${error.message}\n\nPlease check your internet connection and GitHub configuration.`,
          'error',
          6000
        );
      }
    }
  }

  async syncProducts() {
    const syncBtn = document.getElementById('sync-products');
    if (!syncBtn) return;

    const originalHTML = syncBtn.innerHTML;
    syncBtn.innerHTML = '<div class="spinner-small"></div> Syncing...';
    syncBtn.disabled = true;

    try {
      console.log('Starting sync process...');
      console.log('Products to sync:', this.products.length);
      console.log('Product IDs:', this.products.map(p => p.id));

      // Call the real sync API endpoint
      const response = await fetch('/api/sync-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          products: this.products,
          previousCategories: [...new Set(this.lastSyncedProducts.map(p => p.category))].filter(Boolean).sort(),
          createdCategories: Array.from(this.createdCategories)
        })
      });

      const result = await response.json();
      console.log('Sync API response:', result);

      if (result.success) {
        this.hasUnsavedChanges = false;
        this.lastSyncedProducts = [...this.products];
        this.lastSyncedCategories = [...new Set(this.products.map(p => p.category))].filter(Boolean).sort();
        this.updateSyncButtonState();

        // Build success message
        let successMessage = `Products synced and deployed successfully!\n\n• ${result.productsCount || this.products.length} products synced\n• GitHub commit: ${result.githubCommit ? 'Success' : 'Skipped'}\n• Cache purged: ${result.cachePurged ? 'Yes' : 'No'}`;

        if (result.categoriesChanged) {
          successMessage += `\n• Categories changed: Yes (${result.currentCategories?.length || 0} total)`;
          successMessage += `\n• Build triggered: ${result.buildTriggered ? 'Yes (immediate)' : 'Auto (via GitHub)'}`;
        }

        if (window.showToast) {
          window.showToast(
            'Sync Successful',
            successMessage,
            'success',
            result.categoriesChanged ? 8000 : 6000
          );
        }
        this.renderProducts(); // Refresh to remove change indicators
      } else {
        console.error('Sync failed:', result.error);
        if (window.showToast) {
          window.showToast(
            'Sync Failed',
            `${result.error || 'Unknown error'}\n\nPlease check the console for more details and try again.`,
            'error',
            6000
          );
        }
      }
    } catch (error) {
      console.error('Sync error:', error);
      if (window.showToast) {
        window.showToast(
          'Sync Error',
          `${error.message}\n\nPlease check your internet connection and try again.`,
          'error',
          6000
        );
      }
    } finally {
      syncBtn.innerHTML = originalHTML;
      syncBtn.disabled = false;
    }
  }

  // Parse image data - handle both old and new formats
  parseImageData(imageDataString) {
    if (!imageDataString || !imageDataString.trim()) {
      return [];
    }

    try {
      // Try to parse as JSON first (new format)
      const parsed = JSON.parse(imageDataString);
      if (Array.isArray(parsed)) {
        // Deduplicate images based on base filename or original URL
        return this.deduplicateImages(parsed);
      }
    } catch (e) {
      // If JSON parsing fails, treat as old format (newline-separated URLs)
      const urls = imageDataString.split('\n').filter(url => url.trim()).map(url => url.trim());
      // Remove duplicate URLs
      return [...new Set(urls)];
    }

    return [];
  }

  // Deduplicate images based on timestamp and image number (more robust)
  deduplicateImages(images) {
    const seen = new Set();
    const deduplicated = [];

    for (const image of images) {
      // Create a unique key based on the core image identifier (without random suffix)
      let key;
      if (typeof image === 'object' && image.variants) {
        // New format: extract the core identifier (img-XXXX-timestamp) without random suffix
        const baseFilename = image.baseFilename || '';
        // More robust pattern to extract just the img-XXXX-timestamp part
        const coreMatch = baseFilename.match(/^(img-\d+-\d+)/);
        key = coreMatch ? coreMatch[1] : baseFilename;

        console.log('🔍 Dedup check - baseFilename:', baseFilename, '→ core key:', key);

        // Additional check: if we already have this key, merge the variants instead of discarding
        if (seen.has(key)) {
          // Find the existing image with this key and merge variants
          const existingImageIndex = deduplicated.findIndex(existing => {
            const existingKey = existing.baseFilename.match(/^(img-\d+-\d+)/);
            return existingKey && existingKey[1] === key;
          });

          if (existingImageIndex !== -1) {
            // Merge variants from both images, preferring newer ones
            const existingImage = deduplicated[existingImageIndex];
            const mergedVariants = { ...existingImage.variants, ...image.variants };
            deduplicated[existingImageIndex] = {
              ...existingImage,
              variants: mergedVariants
            };
            console.log('🔄 Merged variants for key:', key, 'Total variants:', Object.keys(mergedVariants).length);
            continue;
          }
        }
      } else {
        // Old format: use the URL itself
        key = image;
      }

      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(image);
        console.log('✅ Keeping image with key:', key);
      } else {
        console.log('🔄 Removing duplicate image with key:', key, '(baseFilename:', image.baseFilename || 'N/A', ')');
      }
    }

    console.log(`✅ Deduplicated ${images.length} → ${deduplicated.length} images`);
    return deduplicated;
  }

  // Get the actual count of base images (not variants)
  getImageCount(product) {
    if (!product.images || !Array.isArray(product.images)) {
      return 0;
    }

    // Check if this is the new format (array of objects with variants)
    if (product.images.length > 0 && typeof product.images[0] === 'object' && product.images[0].variants) {
      // New format: count the number of base images (each object represents one base image)
      return product.images.length;
    } else {
      // Old format: array of URL strings
      return product.images.filter(url => url && typeof url === 'string' && url.trim()).length;
    }
  }

  // Utility function to escape HTML and prevent XSS
  escapeHtml(text) {
    if (typeof text !== 'string') return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Book-specific functionality
  handleCategoryChange(category) {
    const bookFields = document.getElementById('book-fields');
    if (!bookFields) return;

    if (category === 'Books') {
      bookFields.style.display = 'block';
    } else {
      bookFields.style.display = 'none';
      this.clearBookFields();
    }
  }

  clearBookFields() {
    const bookDetails = document.getElementById('book-details');
    if (bookDetails) {
      bookDetails.style.display = 'none';
    }
    // Clear all book-related fields (excluding ISBN)
    const fields = ['book-olid', 'book-authors', 'book-publisher', 'book-published-date',
                   'book-page-count', 'book-language', 'book-rating', 'book-ratings-count'];
    fields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) field.value = '';
    });
    const resultsDiv = document.getElementById('book-search-results');
    if (resultsDiv) {
      resultsDiv.style.display = 'none';
      resultsDiv.innerHTML = '';
    }
  }

  async lookupBookByOLID() {
    const olidInput = document.getElementById('book-olid');
    const lookupBtn = document.getElementById('lookup-olid');

    if (!olidInput || !lookupBtn) return;

    const olid = olidInput.value.trim();
    if (!olid) {
      this.showToast('Please enter an OLID', 'error');
      return;
    }

    console.log('[ADMIN PANEL] Book lookup initiated with OLID:', olid);

    lookupBtn.disabled = true;
    lookupBtn.textContent = 'Looking up...';

    try {
      const response = await fetch(`/api/book-lookup?olid=${encodeURIComponent(olid)}`);
      console.log('[ADMIN PANEL] Fetching /api/book-lookup?olid=', olid, 'Response status:', response.status);
      if (!response.ok) {
        this.showToast(`Lookup failed: ${response.statusText}`, 'error');
        return;
      }
      const result = await response.json();
      console.log('[ADMIN PANEL] Book lookup API response:', result);

      // The API returns bookData directly, not wrapped in {success, bookData}
      if (result && (result.name || result.title)) {
        this.populateBookFields({
          title: result.name || result.title,
          authors: result.authors,
          publisher: result.publisher,
          publishedDate: result.publishedDate,
          description: result.description,
          pageCount: result.pageCount,
          language: result.language,
          averageRating: result.averageRating,
          ratingsCount: result.ratingsCount
        }, olid);

        this.showToast('Book details found and populated!', 'success');
      } else if (result && result.error) {
        this.showToast(result.error || `No book found for OLID: ${olid}`, 'error');
      } else {
        this.showToast(`No book found for OLID: ${olid}`, 'error');
      }
    } catch (error) {
      console.error('[ADMIN PANEL] Error looking up book:', error);
      this.showToast('Error looking up book details', 'error');
    } finally {
      lookupBtn.disabled = false;
      lookupBtn.textContent = 'Lookup by OLID';
    }
  }

  async searchBookByTitle() {
    const searchInput = document.getElementById('book-search');
    const searchBtn = document.getElementById('search-book');
    const resultsDiv = document.getElementById('book-search-results');

    if (!searchInput || !searchBtn || !resultsDiv) return;

    const title = searchInput.value.trim();
    if (!title) {
      this.showToast('Please enter a book title', 'error');
      return;
    }

    searchBtn.disabled = true;
    searchBtn.textContent = 'Searching...';
    resultsDiv.style.display = 'none';
    resultsDiv.innerHTML = '';

    try {
      const response = await fetch(`/api/book-lookup?name=${encodeURIComponent(title)}`);
      if (!response.ok) throw new Error('Failed to fetch');
      const data = await response.json();

      // Handle the API response format: { success: true, books: [...] }
      if (data.success && Array.isArray(data.books) && data.books.length > 0) {
        // Show results list
        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = data.books.map((book, idx) => `
          <div class="book-result-item" data-idx="${idx}" style="padding:8px;border-bottom:1px solid #eee;cursor:pointer;">
            <strong>${this.escapeHtml(book.title || book.name)}</strong>
            ${book.authors ? `<span> by ${this.escapeHtml(Array.isArray(book.authors) ? book.authors.join(', ') : book.authors)}</span>` : ''}
            ${book.olid ? `<span style="margin-left:8px;">OLID: ${this.escapeHtml(book.olid)}</span>` : ''}
          </div>
        `).join('');
        // Click handler for selection
        Array.from(resultsDiv.querySelectorAll('.book-result-item')).forEach(item => {
          item.addEventListener('click', () => {
            const idx = parseInt(item.getAttribute('data-idx'), 10);
            const book = data.books[idx];
            this.populateBookFields(book);
            resultsDiv.style.display = 'none';
            this.showToast('Book details populated!', 'success');
          });
        });
      } else if (data.success && Array.isArray(data.books) && data.books.length === 0) {
        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = '<div style="padding:8px;">No books found for that name.</div>';
      } else if (!data.success && data.error) {
        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = `<div style="padding:8px;color:red;">Error: ${this.escapeHtml(data.error)}</div>`;
      } else {
        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = '<div style="padding:8px;">No books found for that name.</div>';
      }
    } catch (error) {
      console.error('Error searching for book:', error);
      resultsDiv.style.display = 'block';
      resultsDiv.innerHTML = '<div style="padding:8px;color:red;">Error searching for book</div>';
    } finally {
      searchBtn.disabled = false;
      searchBtn.textContent = 'Search Book';
    }
  }

  populateBookFields(book) {
    const bookDetails = document.getElementById('book-details');
    if (bookDetails) {
      bookDetails.style.display = 'block';
    }
    // Populate fields (leave empty if not available so user can manually edit)
    const fields = {
      'book-authors': Array.isArray(book.authors) ? book.authors.join(', ') : (book.authors || ''),
      'book-publisher': book.publisher || '',
      'book-published-date': book.publishedDate || '',
      'book-page-count': book.pageCount && book.pageCount > 0 ? book.pageCount.toString() : '',
      'book-language': book.language || '',
      'book-rating': book.averageRating ? `${book.averageRating}/5` : '',
      'book-ratings-count': book.ratingsCount ? `${book.ratingsCount}` : ''
    };
    Object.entries(fields).forEach(([fieldId, value]) => {
      const field = document.getElementById(fieldId);
      if (field) {
        field.value = value;
      }
    });
    // Update product name and description if they're empty
    const nameField = document.getElementById('product-name');
    const descField = document.getElementById('product-description');
    if (nameField && !nameField.value && (book.title || book.name)) {
      nameField.value = book.title || book.name;
    }
    if (descField && !descField.value && book.description) {
      descField.value = book.description;
    }
  }

  showToast(message, type = 'info') {
    if (window.showToast) {
      window.showToast('Book Lookup', message, type, 3000);
    } else {
      alert(message);
    }
  }
}

// Export for use in other modules
window.ModernAdminPanel = ModernAdminPanel;
