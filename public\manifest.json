{"name": "Cheers Marketplace", "short_name": "Cheers", "description": "Family-run marketplace in Chico, CA selling gently used goods with personal inspection", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2563eb", "orientation": "portrait-primary", "scope": "/", "lang": "en-US", "categories": ["shopping", "marketplace", "lifestyle"], "icons": [{"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "/images/logo.png", "sizes": "any", "type": "image/svg+xml", "purpose": "any maskable"}], "screenshots": [{"src": "/images/screenshot-wide.jpg", "sizes": "1280x720", "type": "image/jpeg", "form_factor": "wide", "label": "Cheers Marketplace Homepage"}, {"src": "/images/screenshot-narrow.jpg", "sizes": "640x1136", "type": "image/jpeg", "form_factor": "narrow", "label": "Cheers Marketplace Mobile"}], "shortcuts": [{"name": "Browse Products", "short_name": "Products", "description": "Browse all available products", "url": "/products/", "icons": [{"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml"}]}, {"name": "Categories", "short_name": "Categories", "description": "Browse by category", "url": "/categories/", "icons": [{"src": "/favicon.svg", "sizes": "any", "type": "image/svg+xml"}]}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}