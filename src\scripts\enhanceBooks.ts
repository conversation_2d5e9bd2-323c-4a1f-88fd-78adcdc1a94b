/**
 * Book Enhancement Script
 * Demonstrates how to enhance products with book data
 */

import type { Product } from '../lib/products';
import { enhanceProductIfBook, batchEnhanceProducts, needsBookEnhancement } from '../utils/bookEnhancement';

// Example usage function
export async function demonstrateBookEnhancement() {
  // Example product that might be a book
  const exampleProduct: Product = {
    id: 'example-book-1',
    name: 'The Great Gatsby ISBN: 9780743273565',
    description: 'Classic American novel by <PERSON><PERSON>. A story of decadence and excess in the Jazz Age.',
    price: 12.99,
    category: 'Literature', // Not in Books category yet
    condition: 'Good',
    images: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  console.log('Original product:', exampleProduct);
  console.log('Needs enhancement:', needsBookEnhancement(exampleProduct));

  // Enhance the product
  const result = await enhanceProductIfBook(exampleProduct);
  
  console.log('Enhancement result:', {
    success: result.success,
    enhanced: result.enhanced,
    error: result.error
  });

  if (result.enhanced) {
    console.log('Enhanced product:', result.product);
    console.log('ISBN info:', result.isbnInfo);
    console.log('Book lookup result:', result.bookLookupResult);
  }

  return result;
}

// Example of batch processing
export async function demonstrateBatchEnhancement() {
  const exampleProducts: Product[] = [
    {
      id: 'book-1',
      name: 'To Kill a Mockingbird',
      description: 'Harper Lee classic novel ISBN-13: 9780061120084',
      price: 14.99,
      category: 'Books',
      condition: 'Excellent',
      images: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'book-2', 
      name: '1984 by George Orwell',
      description: 'Dystopian novel. ISBN: 0451524934',
      price: 13.99,
      category: 'Literature',
      condition: 'Good',
      images: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    {
      id: 'not-book',
      name: 'Vintage Lamp',
      description: 'Beautiful vintage table lamp',
      price: 45.00,
      category: 'Home & Decor',
      condition: 'Good',
      images: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  console.log('Processing batch of products...');
  const results = await batchEnhanceProducts(exampleProducts);
  
  results.forEach((result, index) => {
    console.log(`Product ${index + 1}:`, {
      original: exampleProducts[index].name,
      enhanced: result.enhanced,
      newCategory: result.product.category,
      hasAuthors: <AUTHORS>
      hasISBN: Boolean(result.product.isbn)
    });
  });

  return results;
}

// Function to check which products in a list need enhancement
export function analyzeProductsForBookEnhancement(products: Product[]) {
  const analysis = products.map(product => ({
    id: product.id,
    name: product.name,
    category: product.category,
    needsEnhancement: needsBookEnhancement(product),
    hasISBN: Boolean(product.isbn || product.isbn10 || product.isbn13),
    hasBookData: Boolean(product.authors?.length || product.publisher),
    isBookCategory: product.category.toLowerCase() === 'books'
  }));

  const stats = {
    total: products.length,
    booksCategory: analysis.filter(p => p.isBookCategory).length,
    needsEnhancement: analysis.filter(p => p.needsEnhancement).length,
    hasISBN: analysis.filter(p => p.hasISBN).length,
    hasBookData: analysis.filter(p => p.hasBookData).length
  };

  return { analysis, stats };
}

// Example usage in a web context (for admin interface)
export async function enhanceBookProductsInBrowser() {
  try {
    // This would typically fetch from your products API
    const response = await fetch('/data/products.json');
    const products: Product[] = await response.json();
    
    // Analyze which products need enhancement
    const { analysis, stats } = analyzeProductsForBookEnhancement(products);
    
    console.log('Book enhancement analysis:', stats);
    
    // Get products that need enhancement
    const productsNeedingEnhancement = products.filter(product => 
      needsBookEnhancement(product)
    );
    
    if (productsNeedingEnhancement.length === 0) {
      console.log('No products need book enhancement');
      return { enhanced: [], stats };
    }
    
    console.log(`Found ${productsNeedingEnhancement.length} products that could be enhanced`);
    
    // Enhance them (in a real scenario, you'd want to do this server-side)
    const enhancementResults = await batchEnhanceProducts(productsNeedingEnhancement);
    
    const successfulEnhancements = enhancementResults.filter(result => 
      result.success && result.enhanced
    );
    
    console.log(`Successfully enhanced ${successfulEnhancements.length} products`);
    
    return {
      enhanced: successfulEnhancements.map(result => result.product),
      stats,
      results: enhancementResults
    };
    
  } catch (error) {
    console.error('Error enhancing book products:', error);
    throw error;
  }
}

// Utility to create a book product from just an ISBN
export async function createBookProductFromISBN(isbn: string, price: number = 0, condition: Product['condition'] = 'Good') {
  const { createBookFromISBN } = await import('../utils/bookEnhancement');
  
  const result = await createBookFromISBN(isbn, {
    id: `book-${isbn}`,
    price,
    condition,
    images: [],
    createdAt: new Date().toISOString()
  });
  
  if (result.success) {
    console.log('Created book product:', result.product);
  } else {
    console.error('Failed to create book product:', result.error);
  }
  
  return result;
}
