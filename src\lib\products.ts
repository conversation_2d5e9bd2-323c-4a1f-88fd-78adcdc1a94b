// Product data management for Astro/Cloudflare Pages
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  condition: 'Poor' | 'Fair' | 'Good' | 'Excellent' | 'New';
  images: string[] | string; // Can be array of URLs (old format) or JSON string (new format)
  featuredImage?: string; // URL of the main/featured image
  keyPoints?: Array<{ label: string; value: string }>;
  defects?: string | null;
  slug?: string;
  createdAt?: string;
  updatedAt?: string;
  // Book-specific fields
  isbn?: string; // ISBN-10 or ISBN-13
  isbn10?: string;
  isbn13?: string;
  authors?: string[];
  publisher?: string;
  publishedDate?: string;
  pageCount?: number;
  language?: string;
  averageRating?: number;
  ratingsCount?: number;
  bookCategories?: string[];
  bookDescription?: string; // Full book description from API
  bookThumbnail?: string; // Book cover from API
  industryIdentifiers?: Array<{
    type: string;
    identifier: string;
  }>;
}

export interface ProductFilters {
  category?: string;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'name' | 'price' | 'newest' | 'oldest';
  sortOrder?: 'asc' | 'desc';
}

// Generate URL-friendly slug from product name
export function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Get the featured image for a product with variant support
export function getFeaturedImage(product: Product, context: string = 'desktop'): string | undefined {
  // Always use server-side version for static generation
  return getFeaturedImageServer(product, context);
}

// Server-side version
function getFeaturedImageServer(product: Product, context: string = 'desktop'): string | undefined {
  try {
    // For server-side, we'll use a simplified approach
    if (product.featuredImage) {
      return product.featuredImage;
    }

    // Handle both old and new formats
    if (typeof product.images === 'string') {
      try {
        const imageData = JSON.parse(product.images);
        if (Array.isArray(imageData) && imageData.length > 0) {
          const firstImage = imageData[0];
          if (firstImage.variants) {
            const variant = firstImage.variants[context] || firstImage.variants.desktop || firstImage.variants.original || Object.values(firstImage.variants)[0];
            return variant || undefined;
          }
        }
      } catch (e) {
        // Old format - newline separated URLs
        const urls = product.images.split('\n').filter((url: string) => url.trim());
        return urls[0] || undefined;
      }
    } else if (Array.isArray(product.images)) {
      return product.images[0] || undefined;
    }
  } catch (error) {
    console.warn('Error getting featured image:', error);
  }

  return undefined;
}

// Client-side version (removed - using server-side only for static generation)

// Get all products (static at build time)
export async function getAllProducts(): Promise<Product[]> {
  try {
    const products = await import('../data/products.json');
    return products.default.map((product: any) => ({
      ...product,
      slug: product.slug || generateSlug(product.name),
      createdAt: product.createdAt || new Date().toISOString(),
      updatedAt: product.updatedAt || new Date().toISOString(),
    }));
  } catch (error) {
    console.error('Error loading products:', error);
    return [];
  }
}

// Get product by slug
export async function getProductBySlug(slug: string): Promise<Product | null> {
  const products = await getAllProducts();
  return products.find(product => product.slug === slug) || null;
}

// Get products by category
export async function getProductsByCategory(category: string): Promise<Product[]> {
  const products = await getAllProducts();
  return products.filter(product => 
    product.category.toLowerCase() === category.toLowerCase()
  );
}

// Filter and sort products
export function filterProducts(products: Product[], filters: ProductFilters): Product[] {
  let filtered = [...products];

  // Filter by category
  if (filters.category) {
    filtered = filtered.filter(product => 
      product.category.toLowerCase() === filters.category!.toLowerCase()
    );
  }

  // Filter by search term
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.category.toLowerCase().includes(searchTerm)
    );
  }

  // Filter by price range
  if (filters.minPrice !== undefined) {
    filtered = filtered.filter(product => product.price >= filters.minPrice!);
  }
  if (filters.maxPrice !== undefined) {
    filtered = filtered.filter(product => product.price <= filters.maxPrice!);
  }

  // Sort products
  if (filters.sortBy) {
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'price':
          comparison = a.price - b.price;
          break;
        case 'newest':
          comparison = new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
          break;
        case 'oldest':
          comparison = new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime();
          break;
      }
      
      return filters.sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  return filtered;
}

// Get unique categories
export function getCategories(products: Product[]): string[] {
  const categories = products.map(product => product.category);
  return [...new Set(categories)].sort();
}

// Get price range
export function getPriceRange(products: Product[]): { min: number; max: number } {
  if (products.length === 0) return { min: 0, max: 0 };
  
  const prices = products.map(product => product.price);
  return {
    min: Math.min(...prices),
    max: Math.max(...prices)
  };
}

// Validate product data
export function validateProduct(product: Partial<Product>): string[] {
  const errors: string[] = [];

  if (!product.name || product.name.trim().length === 0) {
    errors.push('Product name is required');
  }

  if (!product.description || product.description.trim().length === 0) {
    errors.push('Product description is required');
  }

  if (!product.price || product.price <= 0) {
    errors.push('Product price must be greater than 0');
  }

  if (!product.category || product.category.trim().length === 0) {
    errors.push('Product category is required');
  }

  if (!product.images || product.images.length === 0) {
    errors.push('At least one product image is required');
  }

  return errors;
}

// Format price for display
export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(price);
}

// Generate product structured data for SEO
export function generateProductStructuredData(product: Product) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.images,
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    brand: {
      '@type': 'Brand',
      name: 'Cheers Marketplace',
    },
  };
}
