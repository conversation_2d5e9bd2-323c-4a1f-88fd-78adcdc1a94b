/**
 * Client-side Image Resizer for Cloudflare Pages
 * Resizes images to multiple variants before upload to Bunny CDN
 */

export class ImageResizer {
  constructor() {
    // Define image variants - now using max dimensions to preserve aspect ratios
    this.variants = {
      thumbnail: { maxWidth: 150, maxHeight: 150, suffix: '-thumb', quality: 0.8 },
      mobile: { maxWidth: 480, maxHeight: 480, suffix: '-mobile', quality: 0.85 },
      tablet: { maxWidth: 768, maxHeight: 768, suffix: '-tablet', quality: 0.85 },
      desktop: { width: null, height: null, suffix: '-desktop', quality: 0.9 }, // Preserve original size
      original: { width: null, height: null, suffix: '-original', quality: 0.95 } // Preserve original size
    };

    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  }

  /**
   * Validate a file before processing
   * @param {File} file - File to validate
   * @returns {Object} - Validation result
   */
  validateFile(file) {
    if (!file) {
      return { valid: false, error: 'No file provided' };
    }

    if (!this.allowedTypes.includes(file.type)) {
      return { valid: false, error: `Unsupported file type: ${file.type}` };
    }

    if (file.size > this.maxFileSize) {
      return { valid: false, error: `File too large: ${(file.size / 1024 / 1024).toFixed(1)}MB (max: 10MB)` };
    }

    return { valid: true };
  }

  /**
   * Process multiple files
   * @param {File[]} files - Array of files to process
   * @param {Function} progressCallback - Progress callback function
   * @returns {Promise<Array>} - Array of processed image results
   */
  async processFiles(files, progressCallback = null) {
    const results = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (progressCallback) {
        progressCallback({
          current: i + 1,
          total: files.length,
          filename: file.name
        });
      }

      try {
        const result = await this.processFile(file);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          originalFile: file,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Process a single file into multiple variants
   * @param {File} file - File to process
   * @returns {Promise<Object>} - Processing result
   */
  async processFile(file) {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Generate base filename
      const baseFilename = this.generateBaseFilename(file.name);

      // Load image
      const img = await this.loadImage(file);

      // Create variants
      const variants = {};
      for (const [variantName, config] of Object.entries(this.variants)) {
        const variant = await this.createVariant(img, config, baseFilename);
        variants[variantName] = variant;
      }

      return {
        success: true,
        originalFile: file,
        baseFilename,
        variants
      };
    } catch (error) {
      return {
        success: false,
        originalFile: file,
        error: error.message
      };
    }
  }

  /**
   * Load image from file
   * @param {File} file - File to load
   * @returns {Promise<HTMLImageElement>} - Loaded image
   */
  loadImage(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Create a variant of the image
   * @param {HTMLImageElement} img - Source image
   * @param {Object} config - Variant configuration
   * @param {string} baseFilename - Base filename
   * @returns {Promise<Object>} - Variant data
   */
  async createVariant(img, config, baseFilename) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Calculate target dimensions
    let targetWidth, targetHeight;

    if (config.width === null || config.height === null) {
      // Preserve original dimensions
      targetWidth = img.width;
      targetHeight = img.height;
    } else if (config.maxWidth && config.maxHeight) {
      // Calculate dimensions that fit within max bounds while preserving aspect ratio
      const aspectRatio = img.width / img.height;
      if (img.width > img.height) {
        targetWidth = Math.min(config.maxWidth, img.width);
        targetHeight = targetWidth / aspectRatio;
        if (targetHeight > config.maxHeight) {
          targetHeight = config.maxHeight;
          targetWidth = targetHeight * aspectRatio;
        }
      } else {
        targetHeight = Math.min(config.maxHeight, img.height);
        targetWidth = targetHeight * aspectRatio;
        if (targetWidth > config.maxWidth) {
          targetWidth = config.maxWidth;
          targetHeight = targetWidth / aspectRatio;
        }
      }
      targetWidth = Math.round(targetWidth);
      targetHeight = Math.round(targetHeight);
    } else {
      // Fixed dimensions (legacy)
      targetWidth = config.width;
      targetHeight = config.height;
    }

    // Set canvas dimensions
    canvas.width = targetWidth;
    canvas.height = targetHeight;

    // Enable image smoothing for better quality
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Draw the image scaled to fit (preserves aspect ratio, no cropping)
    ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

    // Convert to blob
    const blob = await new Promise((resolve) => {
      canvas.toBlob(resolve, 'image/webp', config.quality);
    });

    // Generate filename
    const filename = `${baseFilename}${config.suffix}.webp`;

    return {
      blob,
      filename,
      width: targetWidth,
      height: targetHeight,
      size: blob.size
    };
  }

  /**
   * Generate base filename from original filename
   * @param {string} originalName - Original filename
   * @returns {string} - Base filename
   */
  generateBaseFilename(originalName) {
    // Remove extension and clean up
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    const cleaned = nameWithoutExt
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Add timestamp for uniqueness (no random suffix)
    const timestamp = Date.now();

    return `${cleaned}-${timestamp}`;
  }
}

// Create singleton instance
export const imageResizer = new ImageResizer();
