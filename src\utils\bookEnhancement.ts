/**
 * Book Enhancement Utilities
 * Automatically detect ISBNs and enhance book products with metadata
 */

import type { Product } from '../lib/products';
import { detectISBN } from './isbnUtils';


export interface BookEnhancementResult {
  success: boolean;
  enhanced: boolean;
  product: Product;
  error?: string;
}

/**
 * Enhance a single product with book data if it's a book
 */
export async function enhanceProductIfBook(product: Product): Promise<BookEnhancementResult> {
  try {
    // Only enhance if in books category
    const isBookCategory = product.category.toLowerCase() === 'books';
    if (!isBookCategory) {
      return {
        success: true,
        enhanced: false,
        product
      };
    }
    // No ISBN lookup performed
    return {
      success: true,
      enhanced: false,
      product
    };
  } catch (error) {
    console.error('Error enhancing product with book data:', error);
    return {
      success: false,
      enhanced: false,
      product,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Batch enhance multiple products
 */
export async function batchEnhanceProducts(products: Product[]): Promise<BookEnhancementResult[]> {
  const results: BookEnhancementResult[] = [];
  
  // Process in batches to avoid overwhelming the API
  const batchSize = 3;
  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    const batchPromises = batch.map(product => enhanceProductIfBook(product));
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Small delay between batches to be respectful to the API
    if (i + batchSize < products.length) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }
  
  return results;
}

/**
 * Check if a product needs book enhancement
 */
export function needsBookEnhancement(product: Product): boolean {
  const isBookCategory = product.category.toLowerCase() === 'books';
  const hasISBN = Boolean(product.isbn || product.isbn10 || product.isbn13);
  const hasBookData = Boolean(product.authors?.length || product.publisher);
  
  // Needs enhancement if:
  // 1. It's in books category but missing book data
  // 2. It has ISBN but not in books category
  // 3. It has ISBN but missing book data
  if (isBookCategory && !hasBookData) return true;
  if (hasISBN && !isBookCategory) return true;
  if (hasISBN && !hasBookData) return true;
  
  // Check if ISBN can be detected from name/description
  const isbnInfo = detectISBN({
    name: product.name,
    description: product.description,
    isbn: product.isbn
  });
  
  return isbnInfo.isValid && (!isBookCategory || !hasBookData);
}

/**
 * Get enhancement suggestions for a product
 */
export function getEnhancementSuggestions(product: Product): string[] {
  const suggestions: string[] = [];
  const isBookCategory = product.category.toLowerCase() === 'books';
  const hasISBN = Boolean(product.isbn || product.isbn10 || product.isbn13);
  const hasBookData = Boolean(product.authors?.length || product.publisher);
  
  // Detect potential ISBN
  const isbnInfo = detectISBN({
    name: product.name,
    description: product.description,
    isbn: product.isbn
  });
  
  if (isbnInfo.isValid && !isBookCategory) {
    suggestions.push('ISBN detected - consider moving to Books category');
  }
  
  if (isBookCategory && !hasISBN && !isbnInfo.isValid) {
    suggestions.push('Book category but no ISBN found - add ISBN for better metadata');
  }
  
  if ((isBookCategory || isbnInfo.isValid) && !hasBookData) {
    suggestions.push('Missing book metadata - run book enhancement to fetch details');
  }
  
  if (isbnInfo.isValid && isbnInfo.detectedFrom !== 'field') {
    suggestions.push(`ISBN detected in ${isbnInfo.detectedFrom} - consider adding to ISBN field`);
  }
  
  return suggestions;
}


/**
 * Validate book product data
 */
export function validateBookProduct(product: Product): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];
  
  if (product.category.toLowerCase() === 'books') {
    if (!product.authors?.length) {
      issues.push('Missing authors');
    }
    
    if (!product.publisher) {
      issues.push('Missing publisher');
    }
    
    if (!product.isbn && !product.isbn10 && !product.isbn13) {
      issues.push('Missing ISBN');
    }
    
    if (!product.publishedDate) {
      issues.push('Missing publication date');
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
}
