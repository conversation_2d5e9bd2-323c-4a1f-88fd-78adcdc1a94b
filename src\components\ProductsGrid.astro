---
import ProductCard from './ProductCard.astro';
import { generateSlug, getFeaturedImage } from '../lib/products';
import { getFeaturedImageVariant } from '../utils/imageVariants';

const { products } = Astro.props;

// Sort products by newest first by default (based on array index - higher index = newer)
const sortedProducts = [...products].sort((a, b) => {
  // If products have createdAt or dateAdded, use that
  if (a.createdAt && b.createdAt) {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  }
  if (a.dateAdded && b.dateAdded) {
    return new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime();
  }
  // Otherwise, use reverse array order (assuming newer products are added later)
  return products.indexOf(b) - products.indexOf(a);
});

// Pre-process products for maximum performance
const optimizedProducts = sortedProducts.map((product: any, index: number) => {
  const slug = generateSlug(product.name);
  // Get different image variants for responsive display - use mobile as primary for grid cards
  const firstImage = getFeaturedImageVariant(product, 'mobile') || getFeaturedImage(product);
  const mobileImage = getFeaturedImageVariant(product, 'mobile');
  const tabletImage = getFeaturedImageVariant(product, 'tablet');

  // Calculate image count based on format
  let imageCount = 0;
  if (typeof product.images === 'string') {
    try {
      const imageData = JSON.parse(product.images);
      imageCount = Array.isArray(imageData) ? imageData.length : 0;
    } catch (e) {
      // Old format - count newline-separated URLs
      imageCount = product.images.split('\n').filter(url => url.trim()).length;
    }
  } else if (Array.isArray(product.images)) {
    imageCount = product.images.length;
  }

  return {
    id: product.id || index.toString(),
    name: product.name,
    slug,
    category: product.category,
    condition: product.condition || 'Good',
    price: Number(product.price || 0),
    displayPrice: `$${Number(product.price || 0).toFixed(2)}`,
    description: product.description || '',
    shortDescription: (product.description || '').substring(0, 100),
    firstImage,
    mobileImage,
    tabletImage,
    imageCount,
    hasMultipleImages: imageCount > 1,
    hasDefects: !!(product.defects && product.defects.trim()),
    keyPoints: product.keyPoints || [],
    index,
    // Book-specific fields
    authors: product.authors,
    publisher: product.publisher,
    publishedDate: product.publishedDate,
    averageRating: product.averageRating,
    ratingsCount: product.ratingsCount,
    pageCount: product.pageCount,
    isbn: product.isbn || product.isbn13 || product.isbn10
  };
});

// Generate structured data for SEO (only first 12 products for performance)
const structuredData = {
  "@context": "https://schema.org",
  "@type": "ItemList",
  "numberOfItems": optimizedProducts.length,
  "itemListElement": optimizedProducts.slice(0, 12).map((product: any, index: number) => ({
    "@type": "Product",
    "position": index + 1,
    "name": product.name,
    "url": `/products/${product.slug}`,
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    }
  }))
};
---

<!-- Structured Data for SEO -->
<script type="application/ld+json" set:html={JSON.stringify(structuredData)} is:inline></script>

<!-- Products Grid Container -->
<div class="products-container">
  <!-- No Results State -->
  <div id="no-results" class="no-results" style="display: none;">
    <div class="no-results-content">
      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
      </svg>
      <h2>No products found</h2>
      <p>Try adjusting your search or filter criteria.</p>
    </div>
  </div>

  <!-- Products Grid -->
  <div id="products-list" class="products-grid">
    {optimizedProducts.map((product: any) => (
      <ProductCard product={product} />
    ))}
  </div>

  <!-- Pagination Controls -->
  <div id="pagination-container" class="pagination-container">
    <!-- Items per page selector -->
    <div class="items-per-page">
      <label for="items-per-page-select">Items per page:</label>
      <select id="items-per-page-select" class="items-per-page-select">
        <option value="12">12</option>
        <option value="24" selected>24</option>
        <option value="48">48</option>
        <option value="96">96</option>
      </select>
    </div>

    <!-- Pagination info and controls -->
    <div id="pagination-info" class="pagination-info">
      <span id="pagination-text">Showing 1-24 of {optimizedProducts.length} products</span>
    </div>

    <!-- Pagination buttons -->
    <div id="pagination-controls" class="pagination-controls">
      <!-- Pagination buttons will be generated by JavaScript -->
    </div>
  </div>
</div>

<!-- Load products filter script -->
<script is:inline>
  // Load filter script only if filter elements are present
  function loadProductsFilter() {
    if (window.productsFilterLoaded) return;
    window.productsFilterLoaded = true;

    const script = document.createElement('script');
    script.src = '/scripts/products-filter.js';
    script.defer = true;
    document.head.appendChild(script);
  }

  // Check for filter elements after DOM is ready
  if (document.readyState === 'complete') {
    if (document.querySelector('.filter-controls, .search-input, .category-filter')) {
      loadProductsFilter();
    }
  } else {
    window.addEventListener('load', function() {
      if (document.querySelector('.filter-controls, .search-input, .category-filter')) {
        loadProductsFilter();
      }
    });
  }
</script>

<style>
  .products-container {
    width: 100%;
    max-width: 100%;
  }
  
  .products-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    padding: 0;
    margin: 0;
    contain: layout style;
  }

  @media (max-width: 1400px) {
    .products-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 0.875rem;
    }
  }

  @media (max-width: 1200px) {
    .products-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 0.75rem;
    }
  }

  @media (max-width: 768px) {
    .products-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
    }
  }

  @media (max-width: 640px) {
    .products-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  @media (max-width: 480px) {
    .products-grid {
      grid-template-columns: 1fr;
      gap: 0.875rem;
    }
  }

  /* Pagination Styles */
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding: 1.5rem 0;
    border-top: 1px solid var(--border);
    gap: 1rem;
    flex-wrap: wrap;
  }

  .items-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
  }

  .items-per-page-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: white;
    font-size: 0.875rem;
    cursor: pointer;
    transition: border-color 0.2s ease;
  }

  .items-per-page-select:hover {
    border-color: var(--primary);
  }

  .items-per-page-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
  }

  .pagination-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-align: center;
    flex: 1;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .page-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border);
    background: white;
    color: var(--text);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    min-width: 2.5rem;
    text-align: center;
  }

  .page-btn:hover {
    background: var(--light-background);
    border-color: var(--primary);
  }

  .page-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
  }

  .page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .page-btn:disabled:hover {
    background: white;
    border-color: var(--border);
  }

  .page-ellipsis {
    padding: 0.5rem 0.25rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
  }

  /* Mobile pagination adjustments */
  @media (max-width: 768px) {
    .pagination-container {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .items-per-page {
      order: 2;
    }

    .pagination-info {
      order: 1;
      flex: none;
    }

    .pagination-controls {
      order: 3;
      justify-content: center;
    }

    .page-btn {
      min-width: 2rem;
      padding: 0.375rem 0.5rem;
    }
  }
  
  .no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-secondary);
  }
  
  .no-results-content svg {
    margin-bottom: 1rem;
    color: var(--muted);
  }
  
  .no-results h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text);
    font-size: 1.5rem;
  }
  
  .no-results p {
    margin: 0;
  }
</style>
