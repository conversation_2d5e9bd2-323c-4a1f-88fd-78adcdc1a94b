---
// Product Info Component
export interface Props {
  name: string;
  category: string;
  shortDescription?: string;
  description: string;
  displayPrice: string;
  keyPoints?: Array<{label: string; value: string}>;
  // Book-specific props
  authors?: string[];
  publisher?: string;
  publishedDate?: string;
  averageRating?: number;
  ratingsCount?: number;
  pageCount?: number;
  isbn?: string;
}

const {
  name,
  category,
  shortDescription,
  description,
  displayPrice,
  keyPoints,
  authors,
  publisher,
  publishedDate,
  averageRating,
  ratingsCount,
  pageCount,
  isbn
} = Astro.props;

// Check if this is a book product
const isBook = category.toLowerCase() === 'books' || <PERSON><PERSON><PERSON>(authors?.length) || <PERSON><PERSON><PERSON>(publisher);

// Format publication year from publishedDate
const publicationYear = publishedDate ? new Date(publishedDate).getFullYear() : null;
---

<div class="product-info">
  <div class="product-header">
    <h3 class="product-name">{name}</h3>
    <span class="product-category">{category}</span>
  </div>



  {/* Book-specific information */}
  {isBook && authors && authors.length > 0 && (
    <div class="book-authors">
      <span class="book-label">By:</span>
      <span class="book-value">{authors.join(', ')}</span>
    </div>
  )}

  {isBook && publisher && (
    <div class="book-publisher">
      <span class="book-label">Publisher:</span>
      <span class="book-value">{publisher}{publicationYear ? ` (${publicationYear})` : ''}</span>
    </div>
  )}

  {isBook && averageRating && (
    <div class="book-rating">
      <div class="rating-stars">
        {Array.from({ length: 5 }, (_, i) => (
          <span class={`star ${i < Math.floor(averageRating) ? 'filled' : ''}`}>★</span>
        ))}
      </div>
      <span class="rating-text">
        {averageRating.toFixed(1)}{ratingsCount ? ` (${ratingsCount} reviews)` : ''}
      </span>
    </div>
  )}

  {shortDescription && (
    <p class="product-description">
      {shortDescription}{description.length > 100 ? '...' : ''}
    </p>
  )}

  <div class="product-footer">
    <div class="product-price">
      <span class="price-amount">{displayPrice}</span>
    </div>

    {/* Product features */}
    {keyPoints && keyPoints.length > 0 && (
      <div class="product-features">
        {keyPoints.slice(0, 2).map((kp) => (
          <span class="feature-badge" title={`${kp.label}: ${kp.value}`}>
            {kp.value}
          </span>
        ))}
        {keyPoints.length > 2 && (
          <span class="feature-more">+{keyPoints.length - 2}</span>
        )}
      </div>
    )}
  </div>
</div>

<style>
  .product-info {
    padding: 0.875rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
  }

  .product-header {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
  }

  .condition-line {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.625rem;
    background: var(--border-light);
    border-radius: var(--radius);
    border: 1px solid var(--border);
    width: fit-content;
    margin-bottom: 0.75rem;
  }

  .condition-label {
    font-size: 0.6875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .product-name {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text);
    margin: 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  .product-category {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.05em;
    opacity: 0.8;
  }

  /* Book-specific styles */
  .book-authors,
  .book-publisher {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.25rem 0;
  }

  .book-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: fit-content;
  }

  .book-value {
    font-size: 0.75rem;
    color: var(--text);
    font-weight: 500;
  }

  .book-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.25rem 0;
  }

  .rating-stars {
    display: flex;
    gap: 0.125rem;
  }

  .star {
    font-size: 0.875rem;
    color: var(--border);
    transition: color 0.2s ease;
  }

  .star.filled {
    color: #fbbf24;
  }

  .rating-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
  }





  .product-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  .product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-top: auto;
    padding-top: 0.75rem;
  }

  .product-price {
    display: flex;
    align-items: center;
  }

  .price-amount {
    font-size: 1.375rem;
    font-weight: 800;
    color: var(--primary);
  }

  .product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
    align-items: center;
    justify-content: flex-end;
  }

  .feature-badge {
    background: var(--light-background);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.6875rem;
    font-weight: 500;
    border: 1px solid var(--border);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80px;
  }

  .feature-more {
    background: var(--border-light);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.6875rem;
    font-weight: 600;
    border: 1px solid var(--border);
    white-space: nowrap;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .product-info {
      padding: 0.75rem;
      gap: 0.5rem;
    }

    .product-header {
      margin-bottom: 0.5rem;
    }

    .condition-line {
      padding: 0.1875rem 0.5rem;
      margin-bottom: 0.5rem;
    }

    .condition-label {
      font-size: 0.625rem;
    }

    .product-name {
      font-size: 1rem;
    }

    .product-category {
      font-size: 0.6875rem;
    }

    .product-description {
      font-size: 0.8125rem;
    }

    .price-amount {
      font-size: 1.25rem;
    }

    .product-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
      padding-top: 0.5rem;
    }

    .product-features {
      justify-content: flex-start;
      width: 100%;
    }

    .feature-badge {
      font-size: 0.625rem;
      padding: 0.1875rem 0.375rem;
      max-width: 70px;
    }
  }

  @media (max-width: 480px) {
    .product-info {
      padding: 0.625rem;
      gap: 0.375rem;
    }

    .product-name {
      font-size: 0.9375rem;
    }

    .price-amount {
      font-size: 1.125rem;
    }

    .feature-badge {
      font-size: 0.5625rem;
      padding: 0.125rem 0.25rem;
      max-width: 60px;
    }
  }
</style>
