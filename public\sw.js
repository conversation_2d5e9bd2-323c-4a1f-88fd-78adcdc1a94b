/**
 * Service Worker for Cheers Marketplace
 * Optimized caching for CDN assets and performance
 */

const CACHE_NAME = 'cheers-marketplace-v1.0';
const CDN_CACHE = 'cdn-assets-v1.0';
const STATIC_CACHE = 'static-assets-v1.0';
const DYNAMIC_CACHE = 'dynamic-content-v1.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/favicon.svg',
  '/images/logo.png',
  '/images/product-placeholder.svg'
];

// CDN domains to cache aggressively
const CDN_DOMAINS = [
  'b-cdn.net',
  'bunnycdn.com',
  'cheersmarketplace.b-cdn.net'
];

// Cache strategies
const CACHE_STRATEGIES = {
  CDN_IMAGES: 'CacheFirst',
  STATIC_ASSETS: 'CacheFirst', 
  DYNAMIC_CONTENT: 'StaleWhileRevalidate',
  API_CALLS: 'NetworkFirst'
};

/**
 * Install event - cache static assets
 */
self.addEventListener('install', event => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then(cache => {
        return cache.addAll(STATIC_ASSETS);
      }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', event => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== CDN_CACHE && 
                cacheName !== STATIC_CACHE && 
                cacheName !== DYNAMIC_CACHE) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients
      self.clients.claim()
    ])
  );
});

/**
 * Check if URL is from a CDN
 */
function isCDNUrl(url) {
  return CDN_DOMAINS.some(domain => url.includes(domain));
}

/**
 * Check if URL is a static asset
 */
function isStaticAsset(url) {
  const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.webp', '.avif', '.svg', '.woff', '.woff2'];
  return staticExtensions.some(ext => url.includes(ext));
}

/**
 * Check if URL is an API call
 */
function isAPICall(url) {
  return url.includes('/api/') || url.includes('api.');
}

/**
 * Cache First strategy for CDN and static assets
 */
async function cacheFirst(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    // Only cache successful responses
    if (networkResponse.status === 200) {
      // Clone the response before caching
      const responseClone = networkResponse.clone();
      
      // Add cache headers for CDN assets
      if (isCDNUrl(request.url)) {
        const headers = new Headers(responseClone.headers);
        headers.set('Cache-Control', 'public, max-age=31536000, immutable');
        headers.set('Expires', new Date(Date.now() + 31536000000).toUTCString());
        
        const optimizedResponse = new Response(responseClone.body, {
          status: responseClone.status,
          statusText: responseClone.statusText,
          headers: headers
        });
        
        cache.put(request, optimizedResponse.clone());
        return optimizedResponse;
      } else {
        cache.put(request, responseClone);
      }
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Cache First failed:', error);
    throw error;
  }
}

/**
 * Stale While Revalidate strategy
 */
async function staleWhileRevalidate(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  // Fetch from network in background
  const networkPromise = fetch(request).then(response => {
    if (response.status === 200) {
      cache.put(request, response.clone());
    }
    return response;
  }).catch(error => {
    console.error('Network request failed:', error);
    return cachedResponse;
  });
  
  // Return cached version immediately if available
  return cachedResponse || networkPromise;
}

/**
 * Network First strategy for API calls
 */
async function networkFirst(request, cacheName) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.status === 200) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Network First failed, trying cache:', error);
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

/**
 * Fetch event - apply caching strategies
 */
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  // Apply caching strategy based on request type
  if (isCDNUrl(request.url)) {
    // CDN assets - Cache First with long TTL
    event.respondWith(cacheFirst(request, CDN_CACHE));
  } else if (isStaticAsset(request.url)) {
    // Static assets - Cache First
    event.respondWith(cacheFirst(request, STATIC_CACHE));
  } else if (isAPICall(request.url)) {
    // API calls - Network First
    event.respondWith(networkFirst(request, DYNAMIC_CACHE));
  } else if (url.pathname.startsWith('/products/') || 
             url.pathname.startsWith('/categories/') ||
             url.pathname === '/') {
    // Dynamic content - Stale While Revalidate
    event.respondWith(staleWhileRevalidate(request, DYNAMIC_CACHE));
  }
});

/**
 * Message event - handle cache management commands
 */
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      })
    );
  }
});

console.log('Service Worker loaded successfully');
